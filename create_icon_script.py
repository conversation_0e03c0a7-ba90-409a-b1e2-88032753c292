#!/usr/bin/env python3
"""
Create app icon using PIL
"""
try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_icon():
        # Create 512x512 image
        size = 512
        img = Image.new('RGBA', (size, size), (42, 82, 152, 255))
        draw = ImageDraw.Draw(img)
        
        # Draw circle background
        center = size // 2
        radius = 240
        draw.ellipse([center-radius, center-radius, center+radius, center+radius], 
                    fill=(42, 82, 152, 255), outline=(255, 255, 255, 8))
        
        # Calculator body
        calc_w, calc_h = 200, 240
        calc_x = center - calc_w // 2
        calc_y = center - calc_h // 2
        draw.rectangle([calc_x, calc_y, calc_x + calc_w, calc_y + calc_h], 
                      fill=(255, 255, 255, 255), outline=(200, 200, 200, 255), width=2)
        
        # Display
        display_w, display_h = 180, 50
        display_x = calc_x + 10
        display_y = calc_y + 15
        draw.rectangle([display_x, display_y, display_x + display_w, display_y + display_h], 
                      fill=(44, 62, 80, 255))
        
        # Display text
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        
        text_bbox = draw.textbbox((0, 0), "123,456", font=font)
        text_w = text_bbox[2] - text_bbox[0]
        text_h = text_bbox[3] - text_bbox[1]
        text_x = display_x + (display_w - text_w) // 2
        text_y = display_y + (display_h - text_h) // 2
        draw.text((text_x, text_y), "123,456", fill=(0, 255, 136, 255), font=font)
        
        # Buttons
        button_size = 25
        start_x = calc_x + 20
        start_y = calc_y + 80
        spacing = 35
        
        buttons = [
            ['7', '8', '9', '÷'],
            ['4', '5', '6', '×'],
            ['1', '2', '3', '='],
            ['0', '.', '+', 'C']
        ]
        
        for row, button_row in enumerate(buttons):
            for col, btn_text in enumerate(button_row):
                x = start_x + col * spacing
                y = start_y + row * spacing
                
                # Button color
                if btn_text in ['÷', '×', '+', 'C']:
                    color = (0, 123, 255, 255)
                    text_color = (255, 255, 255, 255)
                elif btn_text == '=':
                    color = (40, 167, 69, 255)
                    text_color = (255, 255, 255, 255)
                else:
                    color = (248, 249, 250, 255)
                    text_color = (73, 80, 87, 255)
                
                # Draw button
                draw.ellipse([x-12, y-12, x+12, y+12], fill=color, outline=(200, 200, 200, 255))
                
                # Button text
                try:
                    btn_font = ImageFont.truetype("arial.ttf", 12)
                except:
                    btn_font = ImageFont.load_default()
                
                btn_bbox = draw.textbbox((0, 0), btn_text, font=btn_font)
                btn_text_w = btn_bbox[2] - btn_bbox[0]
                btn_text_h = btn_bbox[3] - btn_bbox[1]
                btn_text_x = x - btn_text_w // 2
                btn_text_y = y - btn_text_h // 2
                draw.text((btn_text_x, btn_text_y), btn_text, fill=text_color, font=btn_font)
        
        # Credit card
        card_x, card_y = calc_x + calc_w + 10, calc_y + 20
        card_w, card_h = 50, 32
        draw.rectangle([card_x, card_y, card_x + card_w, card_y + card_h], 
                      fill=(76, 175, 80, 255), outline=(255, 255, 255, 255), width=2)
        
        # Coins
        coin1_x, coin1_y = card_x + 10, card_y + 50
        coin2_x, coin2_y = card_x + 25, card_y + 65
        
        for coin_x, coin_y, coin_r in [(coin1_x, coin1_y, 15), (coin2_x, coin2_y, 12)]:
            draw.ellipse([coin_x-coin_r, coin_y-coin_r, coin_x+coin_r, coin_y+coin_r], 
                        fill=(255, 215, 0, 255), outline=(255, 255, 255, 255), width=1)
            
            # Coin text
            try:
                coin_font = ImageFont.truetype("arial.ttf", 8)
            except:
                coin_font = ImageFont.load_default()
            
            coin_text = "د.ع"
            coin_bbox = draw.textbbox((0, 0), coin_text, font=coin_font)
            coin_text_w = coin_bbox[2] - coin_bbox[0]
            coin_text_h = coin_bbox[3] - coin_bbox[1]
            coin_text_x = coin_x - coin_text_w // 2
            coin_text_y = coin_y - coin_text_h // 2
            draw.text((coin_text_x, coin_text_y), coin_text, fill=(139, 69, 19, 255), font=coin_font)
        
        # Title
        try:
            title_font = ImageFont.truetype("arial.ttf", 24)
            subtitle_font = ImageFont.truetype("arial.ttf", 16)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
        
        title_text = "محاسب ديون"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_w = title_bbox[2] - title_bbox[0]
        title_x = center - title_w // 2
        title_y = center + 150
        draw.text((title_x, title_y), title_text, fill=(255, 255, 255, 255), font=title_font)
        
        subtitle_text = "احترافي"
        subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
        subtitle_w = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = center - subtitle_w // 2
        subtitle_y = title_y + 30
        draw.text((subtitle_x, subtitle_y), subtitle_text, fill=(255, 255, 255, 230), font=subtitle_font)
        
        # Save icon
        img.save('assets/icons/app_icon.png', 'PNG')
        print("✅ App icon created successfully!")
        
        # Create different sizes
        sizes = [192, 144, 96, 72, 48, 36]
        for s in sizes:
            resized = img.resize((s, s), Image.Resampling.LANCZOS)
            resized.save(f'assets/icons/app_icon_{s}.png', 'PNG')
            print(f"✅ Created {s}x{s} icon")
        
        return True
    
    if __name__ == "__main__":
        create_icon()

except ImportError:
    print("❌ PIL not available. Creating simple icon...")
    
    # Create simple SVG icon
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <circle cx="256" cy="256" r="240" fill="#2a5298" stroke="#ffffff" stroke-width="8"/>
  <rect x="180" y="150" width="152" height="200" rx="12" fill="#ffffff"/>
  <rect x="195" y="170" width="122" height="40" rx="6" fill="#2c3e50"/>
  <text x="256" y="195" text-anchor="middle" fill="#00ff88" font-size="20" font-family="Arial">123,456</text>
  <circle cx="210" cy="240" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="210" y="245" text-anchor="middle" fill="#495057" font-size="10">7</text>
  <circle cx="256" cy="240" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="256" y="245" text-anchor="middle" fill="#495057" font-size="10">8</text>
  <circle cx="302" cy="240" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="302" y="245" text-anchor="middle" fill="#495057" font-size="10">9</text>
  <rect x="340" y="170" width="40" height="25" rx="4" fill="#4CAF50"/>
  <circle cx="380" cy="220" r="12" fill="#FFD700"/>
  <text x="256" y="400" text-anchor="middle" fill="#ffffff" font-size="32" font-family="Arial">محاسب ديون</text>
  <text x="256" y="430" text-anchor="middle" fill="#ffffff" font-size="20" font-family="Arial">احترافي</text>
</svg>'''
    
    with open('assets/icons/app_icon.svg', 'w', encoding='utf-8') as f:
        f.write(svg_content)
    print("✅ SVG icon created!")
