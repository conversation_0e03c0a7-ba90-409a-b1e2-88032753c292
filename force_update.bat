@echo off
title Force Update Flutter App
color 0C

echo.
echo ========================================
echo    FORCE UPDATE FLUTTER APP
echo ========================================
echo.

echo 🧹 Step 1: Deep Clean...
flutter clean
if %ERRORLEVEL% neq 0 (
    echo ❌ Flutter clean failed
    pause
    exit /b 1
)

echo.
echo 🗑️ Step 2: Remove build artifacts...
if exist "build" rmdir /s /q "build"
if exist ".dart_tool" rmdir /s /q ".dart_tool"
if exist ".flutter-plugins" del ".flutter-plugins"
if exist ".flutter-plugins-dependencies" del ".flutter-plugins-dependencies"
if exist ".packages" del ".packages"
if exist "pubspec.lock" del "pubspec.lock"

echo.
echo 📦 Step 3: Get dependencies...
flutter pub get
if %ERRORLEVEL% neq 0 (
    echo ❌ Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo 🔧 Step 4: Check devices...
flutter devices

echo.
echo 🚀 Step 5: Build and install fresh app...
flutter run --hot --verbose --enable-software-rendering

echo.
echo ✅ Force update completed!
pause
