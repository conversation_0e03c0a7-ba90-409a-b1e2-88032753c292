import 'package:flutter/foundation.dart';
import '../models/card_profit.dart';
import '../database/database_helper.dart';

class CardProfitProvider with ChangeNotifier {
  final DatabaseHelper _databaseHelper = DatabaseHelper();
  List<CardProfit> _profits = [];
  bool _isLoading = false;

  List<CardProfit> get profits => _profits;
  bool get isLoading => _isLoading;

  // تحميل جميع الأرباح
  Future<void> loadProfits() async {
    _isLoading = true;
    notifyListeners();

    try {
      _profits = await _databaseHelper.getAllCardProfits();
    } catch (e) {
      debugPrint('Error loading profits: $e');
      _profits = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // إضافة ربح جديد
  Future<void> addProfit(CardProfit profit) async {
    try {
      await _databaseHelper.insertCardProfit(profit);
      await loadProfits();
    } catch (e) {
      debugPrint('Error adding profit: $e');
      rethrow;
    }
  }

  // تحديث ربح
  Future<void> updateProfit(CardProfit profit) async {
    try {
      await _databaseHelper.updateCardProfit(profit);
      await loadProfits();
    } catch (e) {
      debugPrint('Error updating profit: $e');
      rethrow;
    }
  }

  // حذف ربح
  Future<void> deleteProfit(int profitId) async {
    try {
      await _databaseHelper.deleteCardProfit(profitId);
      await loadProfits();
    } catch (e) {
      debugPrint('Error deleting profit: $e');
      rethrow;
    }
  }

  // الحصول على إجمالي الأرباح
  double getTotalProfits() {
    return _profits.fold(0.0, (sum, profit) => sum + profit.totalProfit);
  }

  // الحصول على أرباح نوع بطاقة معين
  List<CardProfit> getProfitsByCardType(String cardType) {
    return _profits.where((profit) => profit.cardType == cardType).toList();
  }

  // الحصول على أرباح فترة معينة
  List<CardProfit> getProfitsByDateRange(DateTime startDate, DateTime endDate) {
    return _profits.where((profit) {
      return profit.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
             profit.date.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  // الحصول على أفضل البطاقات ربحاً
  List<Map<String, dynamic>> getTopProfitableCards() {
    final Map<String, double> cardProfits = {};
    final Map<String, int> cardQuantities = {};

    for (final profit in _profits) {
      cardProfits[profit.cardType] = (cardProfits[profit.cardType] ?? 0) + profit.totalProfit;
      cardQuantities[profit.cardType] = (cardQuantities[profit.cardType] ?? 0) + profit.quantity;
    }

    final List<Map<String, dynamic>> result = [];
    cardProfits.forEach((cardType, totalProfit) {
      result.add({
        'cardType': cardType,
        'totalProfit': totalProfit,
        'quantity': cardQuantities[cardType] ?? 0,
        'averageProfit': totalProfit / (cardQuantities[cardType] ?? 1),
      });
    });

    result.sort((a, b) => b['totalProfit'].compareTo(a['totalProfit']));
    return result;
  }

  // الحصول على إحصائيات الأرباح اليومية
  Map<String, double> getDailyProfits() {
    final Map<String, double> dailyProfits = {};
    
    for (final profit in _profits) {
      final dateKey = '${profit.date.year}-${profit.date.month.toString().padLeft(2, '0')}-${profit.date.day.toString().padLeft(2, '0')}';
      dailyProfits[dateKey] = (dailyProfits[dateKey] ?? 0) + profit.totalProfit;
    }
    
    return dailyProfits;
  }

  // الحصول على إحصائيات الأرباح الشهرية
  Map<String, double> getMonthlyProfits() {
    final Map<String, double> monthlyProfits = {};
    
    for (final profit in _profits) {
      final monthKey = '${profit.date.year}-${profit.date.month.toString().padLeft(2, '0')}';
      monthlyProfits[monthKey] = (monthlyProfits[monthKey] ?? 0) + profit.totalProfit;
    }
    
    return monthlyProfits;
  }
}
