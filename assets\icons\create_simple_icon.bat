@echo off
echo Creating simple app icon...

REM Create a simple colored square icon using Windows tools
echo Creating app icon using Windows tools...

REM Try to create using PowerShell
powershell -Command "Add-Type -AssemblyName System.Drawing; $bmp = New-Object System.Drawing.Bitmap(512, 512); $graphics = [System.Drawing.Graphics]::FromImage($bmp); $brush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(42, 82, 152)); $graphics.FillRectangle($brush, 0, 0, 512, 512); $pen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 8); $graphics.DrawEllipse($pen, 16, 16, 480, 480); $whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White); $graphics.FillRectangle($whiteBrush, 156, 156, 200, 200); $font = New-Object System.Drawing.Font('Arial', 24, [System.Drawing.FontStyle]::Bold); $graphics.DrawString('محاسب ديون', $font, $whiteBrush, 180, 400); $bmp.Save('assets/icons/app_icon.png', [System.Drawing.Imaging.ImageFormat]::Png); $graphics.Dispose(); $bmp.Dispose(); Write-Host 'Icon created successfully!'"

if exist "assets\icons\app_icon.png" (
    echo ✅ App icon created successfully!
) else (
    echo ❌ Failed to create icon with PowerShell
    echo Creating fallback icon...
    
    REM Create a simple text file as fallback
    echo Creating fallback...
    copy /y NUL "assets\icons\app_icon.png" >NUL 2>&1
)

echo Done!
