class NotificationModel {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime createdAt;
  final String? debtId;
  final String? customerId;
  final String? customerName;
  final double? amount;
  final DateTime? dueDate;
  final bool isRead;
  final Map<String, dynamic>? additionalData;

  NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.priority,
    required this.createdAt,
    this.debtId,
    this.customerId,
    this.customerName,
    this.amount,
    this.dueDate,
    this.isRead = false,
    this.additionalData,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => NotificationType.general,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.toString() == json['priority'],
        orElse: () => NotificationPriority.medium,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      debtId: json['debtId'],
      customerId: json['customerId'],
      customerName: json['customerName'],
      amount: json['amount']?.toDouble(),
      dueDate: json['dueDate'] != null ? DateTime.parse(json['dueDate']) : null,
      isRead: json['isRead'] ?? false,
      additionalData: json['additionalData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.toString(),
      'priority': priority.toString(),
      'createdAt': createdAt.toIso8601String(),
      'debtId': debtId,
      'customerId': customerId,
      'customerName': customerName,
      'amount': amount,
      'dueDate': dueDate?.toIso8601String(),
      'isRead': isRead,
      'additionalData': additionalData,
    };
  }

  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    DateTime? createdAt,
    String? debtId,
    String? customerId,
    String? customerName,
    double? amount,
    DateTime? dueDate,
    bool? isRead,
    Map<String, dynamic>? additionalData,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      debtId: debtId ?? this.debtId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      amount: amount ?? this.amount,
      dueDate: dueDate ?? this.dueDate,
      isRead: isRead ?? this.isRead,
      additionalData: additionalData ?? this.additionalData,
    );
  }
}

enum NotificationType {
  dueSoon,      // مستحق قريباً
  dueToday,     // مستحق اليوم
  overdue,      // متأخر
  payment,      // تسديد
  lowStock,     // مخزون منخفض
  outOfStock,   // نفاد المخزون
  general,      // عام
}

enum NotificationPriority {
  low,          // منخفض
  medium,       // متوسط
  high,         // عالي
  urgent,       // عاجل
}

extension NotificationTypeExtension on NotificationType {
  String get displayName {
    switch (this) {
      case NotificationType.dueSoon:
        return 'مستحق قريباً';
      case NotificationType.dueToday:
        return 'مستحق اليوم';
      case NotificationType.overdue:
        return 'متأخر';
      case NotificationType.payment:
        return 'تسديد';
      case NotificationType.lowStock:
        return 'مخزون منخفض';
      case NotificationType.outOfStock:
        return 'نفاد المخزون';
      case NotificationType.general:
        return 'عام';
    }
  }

  String get icon {
    switch (this) {
      case NotificationType.dueSoon:
        return '⏰';
      case NotificationType.dueToday:
        return '🔔';
      case NotificationType.overdue:
        return '⚠️';
      case NotificationType.payment:
        return '💰';
      case NotificationType.lowStock:
        return '📦';
      case NotificationType.outOfStock:
        return '❌';
      case NotificationType.general:
        return 'ℹ️';
    }
  }
}

extension NotificationPriorityExtension on NotificationPriority {
  String get displayName {
    switch (this) {
      case NotificationPriority.low:
        return 'منخفض';
      case NotificationPriority.medium:
        return 'متوسط';
      case NotificationPriority.high:
        return 'عالي';
      case NotificationPriority.urgent:
        return 'عاجل';
    }
  }
}
