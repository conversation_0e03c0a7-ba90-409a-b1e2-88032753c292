# 🚀 دليل تحسين أداء Flutter

## ⚡ طرق تسريع التطبيق

### 1. **استخدام الملفات المحسنة**
```bash
# للتشغيل السريع
performance_boost.bat

# لتحسين المحاكي
emulator_boost.bat
```

### 2. **أوامر التشغيل السريع**
```bash
# تشغيل عادي مع تحسينات
flutter run --hot --enable-software-rendering

# تشغيل Profile Mode (الأسرع)
flutter run --profile

# تشغيل مع تقليل الذاكرة
flutter run --hot --dart-define=flutter.inspector.structuredErrors=false
```

### 3. **تحسين المحاكي**
- **زيادة RAM المحاكي**: 4GB أو أكثر
- **تفعيل Hardware Acceleration**: Intel HAXM أو AMD-V
- **استخدام API Level حديث**: API 30+ مستحسن

### 4. **تحسين VS Code**
- تم تحسين إعدادات `.vscode/settings.json`
- تفعيل Hot Reload التلقائي
- استبعاد المجلدات غير الضرورية

### 5. **تحسين Gradle**
- تم تحسين `android/gradle.properties`
- زيادة الذاكرة المخصصة
- تفعيل البناء المتوازي

## 🔧 نصائح إضافية

### للمطورين:
- استخدم `Hot Reload` (r) بدلاً من `Hot Restart` (R)
- أغلق التطبيقات الأخرى أثناء التطوير
- استخدم `flutter clean` عند المشاكل

### لتحسين الكود:
- استخدم `const` constructors
- تجنب `setState` المفرط
- استخدم `ListView.builder` للقوائم الطويلة

### للاختبار:
- استخدم `Profile Mode` للاختبار النهائي
- اختبر على جهاز حقيقي للأداء الفعلي
- استخدم Flutter Inspector لتحليل الأداء

## 📱 إعدادات المحاكي المثلى

### Android Studio AVD:
- **RAM**: 4GB
- **VM Heap**: 512MB
- **Graphics**: Hardware - GLES 2.0
- **Boot Option**: Cold Boot

### إعدادات إضافية:
```
-gpu swiftshader_indirect
-memory 4096
-cores 4
```

## 🚨 استكشاف الأخطاء

### إذا كان التطبيق بطيء:
1. `flutter clean`
2. `flutter pub get`
3. إعادة تشغيل المحاكي
4. استخدام Profile Mode

### إذا فشل البناء:
1. تحقق من مساحة القرص
2. أعد تشغيل Android Studio
3. `flutter doctor` للتحقق من المشاكل

## ✅ قائمة التحقق السريع

- [ ] تم تشغيل `performance_boost.bat`
- [ ] المحاكي يستخدم Hardware Acceleration
- [ ] VS Code محسن للأداء
- [ ] استخدام Hot Reload بدلاً من Restart
- [ ] إغلاق التطبيقات غير الضرورية

---
**💡 نصيحة**: استخدم Profile Mode للاختبار النهائي دائماً!
