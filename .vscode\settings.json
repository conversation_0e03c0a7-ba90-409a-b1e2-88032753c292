{"files.autoSave": "after<PERSON>elay", "files.autoSaveDelay": 1000, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "flutter.hotReloadOnSave": true, "dart.flutterHotReloadOnSave": true, "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "files.watcherExclude": {"**/.git/objects/**": true, "**/.git/subtree-cache/**": true, "**/node_modules/*/**": true, "**/.hg/store/**": true, "**/build/**": true, "**/.dart_tool/**": true, "**/ios/Pods/**": true, "**/android/.gradle/**": true, "**/android/build/**": true}, "search.exclude": {"**/build": true, "**/.dart_tool": true, "**/ios/Pods": true, "**/android/.gradle": true, "**/android/build": true}, "files.exclude": {"**/.dart_tool": true, "**/build": true}, "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.tabCompletion": "on", "editor.wordBasedSuggestions": "off"}