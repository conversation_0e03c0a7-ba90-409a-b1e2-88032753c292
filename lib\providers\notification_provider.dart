import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/notification_settings.dart';

class NotificationProvider extends ChangeNotifier {
  NotificationSettings _settings = NotificationSettings();
  
  NotificationSettings get settings => _settings;

  // Load settings from SharedPreferences
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('notification_settings');
      
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson);
        _settings = NotificationSettings.fromJson(settingsMap);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }



  // Update specific setting
  Future<void> updateSalesNotifications(bool enabled) async {
    _settings = _settings.copyWith(salesNotificationsEnabled: enabled);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  Future<void> updateInventoryNotifications(bool enabled) async {
    _settings = _settings.copyWith(inventoryNotificationsEnabled: enabled);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  Future<void> updateNotificationDuration(int duration) async {
    _settings = _settings.copyWith(notificationDuration: duration);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  Future<void> updateLowStockThreshold(int threshold) async {
    _settings = _settings.copyWith(lowStockThreshold: threshold);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  Future<void> updateDailyReminder(bool enabled) async {
    _settings = _settings.copyWith(dailyInventoryReminder: enabled);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  Future<void> updateReminderTime(String time) async {
    _settings = _settings.copyWith(reminderTime: time);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  // Private method to save settings to storage
  Future<void> _saveSettingsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(_settings.toJson());
      await prefs.setString('notification_settings', settingsJson);
    } catch (e) {
      debugPrint('Error saving notification settings: $e');
    }
  }

  // Show notification methods
  void showSalesNotification(BuildContext context, {
    required String cardType,
    required int quantity,
    required double amount,
    required String customerName,
  }) {
    if (!_settings.salesNotificationsEnabled) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.shopping_cart,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      '🎉 تم إضافة دين جديد',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'العميل: $customerName',
                      style: const TextStyle(fontSize: 14, color: Colors.white),
                    ),
                    Text(
                      'النوع: $cardType • الكمية: $quantity',
                      style: const TextStyle(fontSize: 13, color: Colors.white),
                    ),
                    Text(
                      'المبلغ: ${amount.toStringAsFixed(0)} د.ع',
                      style: const TextStyle(fontSize: 13, color: Colors.white),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.green,
        duration: Duration(seconds: _settings.notificationDuration),
        behavior: SnackBarBehavior.fixed,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(12),
          ),
        ),
      ),
    );
  }

  // Enhanced sales notification with inventory info
  void showEnhancedSalesNotification(BuildContext context, {
    required String cardType,
    required int quantity,
    required double amount,
    required String customerName,
    required int remainingStock,
    required bool isLowStock,
    required bool isOutOfStock,
  }) {
    if (!_settings.salesNotificationsEnabled) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Container(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Sales info
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.shopping_cart,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '🎉 تم إضافة دين جديد',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          'العميل: $customerName',
                          style: const TextStyle(fontSize: 14, color: Colors.white),
                        ),
                        Text(
                          'النوع: $cardType • الكمية: $quantity كارت',
                          style: const TextStyle(fontSize: 13, color: Colors.white),
                        ),
                        Text(
                          'المبلغ: ${amount.toStringAsFixed(0)} د.ع',
                          style: const TextStyle(fontSize: 13, color: Colors.white),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Inventory status
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      isOutOfStock ? Icons.warning : Icons.inventory_2,
                      color: isOutOfStock ? Colors.red.shade200 :
                             isLowStock ? Colors.orange.shade200 : Colors.green.shade200,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isOutOfStock
                          ? '⚠️ نفد المخزون من $cardType'
                          : isLowStock
                            ? '📦 مخزون منخفض: $remainingStock كارت متبقي'
                            : '✅ المخزون: $remainingStock كارت متبقي',
                        style: TextStyle(
                          fontSize: 12,
                          color: isOutOfStock ? Colors.red.shade200 :
                                 isLowStock ? Colors.orange.shade200 : Colors.green.shade200,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        backgroundColor: Colors.green,
        duration: Duration(seconds: _settings.notificationDuration + 2),
        behavior: SnackBarBehavior.fixed,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(12),
          ),
        ),
      ),
    );
  }

  void showInventoryNotification(BuildContext context, {
    required String cardType,
    required int remainingQuantity,
    required bool isOutOfStock,
  }) {
    if (!_settings.inventoryNotificationsEnabled) return;

    final isLowStock = remainingQuantity <= _settings.lowStockThreshold && remainingQuantity > 0;

    if (!isOutOfStock && !isLowStock) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isOutOfStock ? Icons.warning : Icons.inventory_2,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isOutOfStock ? '⚠️ نفدت الكمية!' : '📦 كمية منخفضة',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'نوع الكارت: $cardType',
                      style: const TextStyle(fontSize: 14, color: Colors.white),
                    ),
                    Text(
                      isOutOfStock
                        ? 'الكمية المتبقية: 0 كارت'
                        : 'الكمية المتبقية: $remainingQuantity كارت',
                      style: const TextStyle(fontSize: 13, color: Colors.white),
                    ),
                    if (!isOutOfStock)
                      const Text(
                        'يرجى إعادة التخزين قريباً',
                        style: TextStyle(fontSize: 12, color: Colors.white),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
        backgroundColor: isOutOfStock ? Colors.red : Colors.orange,
        duration: Duration(seconds: _settings.notificationDuration + 1),
        behavior: SnackBarBehavior.fixed,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(12),
            bottomRight: Radius.circular(12),
          ),
        ),
      ),
    );
  }
}
