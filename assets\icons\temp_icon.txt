# Temporary App Icon Instructions

Since we cannot generate PNG files directly, here are the steps to create the app icon:

1. Open the file: assets/icons/create_icon.html in a web browser
2. Take a screenshot of the icon displayed
3. Save it as app_icon.png (512x512 pixels)
4. Place it in the assets/icons/ folder

The icon design includes:
- Blue gradient circular background
- White calculator with display showing "123,456"
- Calculator buttons in a grid layout
- Credit card icon (green)
- Gold coins with "د.ع" text
- Arabic title "محاسب ديون احترافي"

Alternative: Use the SVG file (app_icon.svg) and convert it to PNG using:
- Online SVG to PNG converters
- Design tools like Canva, Figma, or GIMP
- Any image editing software

Required sizes:
- Main icon: 512x512 px
- Additional sizes: 192, 144, 96, 72, 48, 36 px
