import 'package:flutter/material.dart';

// Widget علامة الصح المتحركة
class AnimatedCheckmarkWidget extends StatefulWidget {
  const AnimatedCheckmarkWidget({super.key});

  @override
  State<AnimatedCheckmarkWidget> createState() => _AnimatedCheckmarkWidgetState();
}

class _AnimatedCheckmarkWidgetState extends State<AnimatedCheckmarkWidget>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _checkController;
  late AnimationController _fadeController;
  
  late Animation<double> _scaleAnimation;
  late Animation<double> _checkAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // تحكم في حجم الدائرة
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    // تحكم في رسم علامة الصح
    _checkController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // تحكم في الاختفاء
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _checkAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _checkController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    // بدء التحريك
    _startAnimation();
  }
  
  void _startAnimation() async {
    // تحريك الدائرة أولاً
    await _scaleController.forward();
    
    // ثم رسم علامة الصح
    await _checkController.forward();
    
    // انتظار قليل
    await Future.delayed(const Duration(milliseconds: 800));
    
    // ثم الاختفاء
    await _fadeController.forward();
    
    // إغلاق النافذة
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _checkController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: AnimatedBuilder(
          animation: Listenable.merge([_scaleAnimation, _checkAnimation, _fadeAnimation]),
          builder: (context, child) {
            return Opacity(
              opacity: _fadeAnimation.value,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withValues(alpha: 0.4),
                        blurRadius: 30,
                        spreadRadius: 10,
                      ),
                    ],
                  ),
                  child: CustomPaint(
                    painter: CheckmarkPainter(_checkAnimation.value),
                    child: const SizedBox.expand(),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

// رسام علامة الصح
class CheckmarkPainter extends CustomPainter {
  CheckmarkPainter(this.progress);

  final double progress;
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 6.0
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;
    
    final center = Offset(size.width / 2, size.height / 2);
    final checkmarkSize = size.width * 0.3;
    
    // نقاط علامة الصح
    final startPoint = Offset(center.dx - checkmarkSize * 0.5, center.dy);
    final middlePoint = Offset(center.dx - checkmarkSize * 0.1, center.dy + checkmarkSize * 0.3);
    final endPoint = Offset(center.dx + checkmarkSize * 0.5, center.dy - checkmarkSize * 0.3);
    
    final path = Path();
    
    if (progress <= 0.5) {
      // رسم الجزء الأول من علامة الصح
      final firstProgress = progress * 2;
      final currentPoint = Offset.lerp(startPoint, middlePoint, firstProgress)!;
      path.moveTo(startPoint.dx, startPoint.dy);
      path.lineTo(currentPoint.dx, currentPoint.dy);
    } else {
      // رسم الجزء الأول كاملاً
      path.moveTo(startPoint.dx, startPoint.dy);
      path.lineTo(middlePoint.dx, middlePoint.dy);
      
      // رسم الجزء الثاني
      final secondProgress = (progress - 0.5) * 2;
      final currentPoint = Offset.lerp(middlePoint, endPoint, secondProgress)!;
      path.lineTo(currentPoint.dx, currentPoint.dy);
    }
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(CheckmarkPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
