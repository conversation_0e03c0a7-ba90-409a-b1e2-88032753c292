@echo off
echo 🚀 تسريع Flutter - Performance Boost Script
echo ==========================================

echo.
echo 🧹 Step 1: تنظيف الملفات المؤقتة...
if exist "build" rmdir /s /q "build"
if exist ".dart_tool" rmdir /s /q ".dart_tool"
if exist ".flutter-plugins" del ".flutter-plugins"
if exist ".flutter-plugins-dependencies" del ".flutter-plugins-dependencies"

echo.
echo 📦 Step 2: تحديث Dependencies...
flutter pub get

echo.
echo ⚡ Step 3: تشغيل مع تحسينات الأداء...
echo اختر نوع التشغيل:
echo [1] تشغيل عادي مع Hot Reload
echo [2] تشغيل سريع (Profile Mode)
echo [3] تشغيل على المحاكي مع تحسينات
echo [4] تشغيل على الجهاز الحقيقي

set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" (
    echo تشغيل عادي مع Hot Reload...
    flutter run --hot --enable-software-rendering
) else if "%choice%"=="2" (
    echo تشغيل Profile Mode للأداء الأفضل...
    flutter run --profile --enable-software-rendering
) else if "%choice%"=="3" (
    echo تشغيل على المحاكي مع تحسينات...
    flutter run --hot --enable-software-rendering --disable-service-auth-codes --host-vmservice-port 0
) else if "%choice%"=="4" (
    echo تشغيل على الجهاز الحقيقي...
    flutter run --release
) else (
    echo خيار غير صحيح، تشغيل عادي...
    flutter run --hot
)

echo.
echo ✅ تم الانتهاء!
pause
