{"flutter": {"hot_reload": {"enabled": true, "auto_save": true, "save_delay_ms": 1000, "watch_directories": ["lib/", "assets/"], "ignore_patterns": ["**/*.g.dart", "**/*.freezed.dart", "**/generated_plugin_registrant.dart", "build/**", ".dart_tool/**"]}, "development": {"preserve_state": true, "fast_refresh": true, "debug_mode": true, "verbose_logging": false}, "build": {"target": "lib/main.dart", "flavor": "development", "dart_defines": [], "tree_shake_icons": true, "track_widget_creation": true}}, "editor": {"auto_save": true, "format_on_save": true, "organize_imports_on_save": true, "fix_imports_on_save": true}, "git": {"auto_commit": false, "commit_message_template": "Auto commit: {timestamp}", "ignore_patterns": ["build/", ".dart_tool/", ".packages", ".metadata", ".flutter-plugins", ".flutter-plugins-dependencies"]}}