import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../providers/debt_provider.dart';
import '../widgets/debts_tab.dart';

class CustomerDebtsScreen extends StatefulWidget {
  const CustomerDebtsScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<CustomerDebtsScreen> createState() => _CustomerDebtsScreenState();
}

class _CustomerDebtsScreenState extends State<CustomerDebtsScreen> {
  @override
  void initState() {
    super.initState();
    // Load customer debts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerDebts(widget.customer.id!);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh debts when returning to this screen
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debtProvider.refreshCurrentCustomerDebts();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('ديون ${widget.customer.name}'),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: DebtsTab(customer: widget.customer),
    );
  }
}
