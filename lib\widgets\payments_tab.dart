import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/customer.dart';
import '../providers/debt_provider.dart';
import '../screens/payment_statistics_screen.dart';
import 'payment_card.dart';

class PaymentsTab extends StatefulWidget {
  const PaymentsTab({super.key, required this.customer});
  final Customer customer;

  @override
  State<PaymentsTab> createState() => _PaymentsTabState();
}

class _PaymentsTabState extends State<PaymentsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool _isSelectionMode = false;
  final Set<int> _selectedPaymentIds = {};

  @override
  void initState() {
    super.initState();
    // Load payments when tab is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerPayments(widget.customer.id!);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Reload payments when dependencies change (e.g., when returning from payment dialog)
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debtProvider.loadCustomerPayments(widget.customer.id!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        if (debtProvider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        // Get all payments for this customer
        final customerPayments = debtProvider.payments
            .where((payment) => payment.customerId == widget.customer.id)
            .toList();

        debugPrint(
          'PaymentsTab: Total payments in provider: ${debtProvider.payments.length}',
        );
        debugPrint(
          'PaymentsTab: Customer ${widget.customer.id} payments: ${customerPayments.length}',
        );

        if (customerPayments.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.payment_outlined, size: 80, color: Colors.grey[400]),
                const SizedBox(height: 16),
                Text(
                  'لا توجد تسديدات',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
                const SizedBox(height: 8),
                Text(
                  'ستظهر التسديدات هنا بعد إجراء عمليات الدفع',
                  style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Action buttons
            Container(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  // Top row - Selection and Statistics buttons
                  if (customerPayments.isNotEmpty) ...[
                    AnimatedSwitcher(
                      duration: const Duration(milliseconds: 200),
                      child: _isSelectionMode
                          ? Row(
                              key: const ValueKey('selection_mode'),
                              children: [
                                Expanded(
                                  child: OutlinedButton.icon(
                                    onPressed: () => _toggleSelectionMode(),
                                    icon: const Icon(Icons.close, size: 18),
                                    label: const Text(
                                      'إلغاء التحديد',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: Colors.red,
                                      side: BorderSide(
                                        color: Colors.red.shade300,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: _selectedPaymentIds.isEmpty
                                        ? null
                                        : () => _deleteSelectedPayments(),
                                    icon: const Icon(Icons.undo, size: 18),
                                    label: Text(
                                      'إرجاع المحدد (${_selectedPaymentIds.length})',
                                      style: const TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          _selectedPaymentIds.isEmpty
                                          ? Colors.grey[400]
                                          : Colors.blue[600],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () => _deleteAllPayments(),
                                    icon: const Icon(Icons.undo, size: 18),
                                    label: const Text(
                                      'إرجاع الكل',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue[700],
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : Row(
                              key: const ValueKey('normal_mode'),
                              children: [
                                Expanded(
                                  child: OutlinedButton.icon(
                                    onPressed: () => _toggleSelectionMode(),
                                    icon: const Icon(Icons.checklist, size: 18),
                                    label: const Text(
                                      'تحديد متعدد',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: Colors.blue,
                                      side: BorderSide(
                                        color: Colors.blue.shade300,
                                      ),
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              PaymentStatisticsScreen(
                                                customer: widget.customer,
                                              ),
                                        ),
                                      );
                                    },
                                    icon: const Icon(Icons.analytics, size: 18),
                                    label: const Text(
                                      'إحصائيات',
                                      style: TextStyle(fontSize: 13),
                                    ),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ],
              ),
            ),

            // Payments List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  await debtProvider.loadCustomerPayments(widget.customer.id!);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  itemCount: customerPayments.length,
                  itemBuilder: (context, index) {
                    final payment = customerPayments[index];
                    // Find the debt for this payment
                    final debt = debtProvider.debts
                        .where((d) => d.id == payment.debtId)
                        .firstOrNull;

                    return GestureDetector(
                      onTap: _isSelectionMode
                          ? () {
                              setState(() {
                                if (payment.id != null) {
                                  if (_selectedPaymentIds.contains(
                                    payment.id!,
                                  )) {
                                    _selectedPaymentIds.remove(payment.id!);
                                  } else {
                                    _selectedPaymentIds.add(payment.id!);
                                  }
                                }
                              });
                            }
                          : null,
                      child: Container(
                        decoration:
                            _isSelectionMode &&
                                payment.id != null &&
                                _selectedPaymentIds.contains(payment.id!)
                            ? BoxDecoration(
                                border: Border.all(
                                  color: Colors.blue,
                                  width: 2,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              )
                            : null,
                        child: PaymentCard(
                          payment: payment,
                          debt: debt,
                          customer: widget.customer,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedPaymentIds.clear();
      }
    });
  }

  void _deleteSelectedPayments() {
    if (_selectedPaymentIds.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.undo, color: Colors.blue[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'إرجاع التسديدات المحددة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من إرجاع ${_selectedPaymentIds.length} تسديد محدد؟\n\nسيتم إرجاع الديون المرتبطة إلى حالة غير مدفوعة.',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final debtProvider = Provider.of<DebtProvider>(
                  context,
                  listen: false,
                );

                for (final paymentId in _selectedPaymentIds) {
                  await debtProvider.reversePayment(paymentId);
                }

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  setState(() {
                    _selectedPaymentIds.clear();
                    _isSelectionMode = false;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم إرجاع ${_selectedPaymentIds.length} تسديد بنجاح',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إرجاع',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  void _deleteAllPayments() {
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerPayments = debtProvider.payments
        .where((payment) => payment.customerId == widget.customer.id)
        .toList();

    if (customerPayments.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(Icons.undo, color: Colors.blue[600], size: 28),
            const SizedBox(width: 12),
            const Text(
              'إرجاع جميع التسديدات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
          ],
        ),
        content: Text(
          'هل أنت متأكد من إرجاع جميع التسديدات (${customerPayments.length} تسديد)؟\n\nسيتم إرجاع جميع الديون إلى حالة غير مدفوعة.',
          style: const TextStyle(fontSize: 16, color: Colors.black87),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(foregroundColor: Colors.grey[600]),
            child: const Text('إلغاء', style: TextStyle(fontSize: 16)),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                for (final payment in customerPayments) {
                  if (payment.id != null) {
                    await debtProvider.reversePayment(payment.id!);
                  }
                }

                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  setState(() {
                    _selectedPaymentIds.clear();
                    _isSelectionMode = false;
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'تم إرجاع ${customerPayments.length} تسديد بنجاح',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted && context.mounted) {
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('حدث خطأ: ${e.toString()}'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              'إرجاع الكل',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }
}
