#!/usr/bin/env python3
"""
Create a better app icon for the debt accounting app
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os

    def create_professional_icon():
        # Create 1024x1024 image for high quality
        size = 1024
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Background gradient circle
        center = size // 2
        radius = 480

        # Create gradient effect
        for i in range(radius):
            alpha = 255 - int(i * 0.1)
            if alpha < 100:
                alpha = 100
            color1 = (30, 60, 114, alpha)  # Dark blue
            color2 = (42, 82, 152, alpha)  # Medium blue
            color3 = (61, 108, 185, alpha)  # Light blue

            if i < radius // 3:
                color = color1
            elif i < 2 * radius // 3:
                color = color2
            else:
                color = color3

            draw.ellipse([center - radius + i, center - radius + i,
                         center + radius - i, center + radius - i],
                        fill=color)

        # White border
        draw.ellipse([center - radius, center - radius,
                     center + radius, center + radius],
                    outline=(255, 255, 255, 255), width=16)

        # Calculator body (larger and more detailed)
        calc_w, calc_h = 320, 400
        calc_x = center - calc_w // 2
        calc_y = center - calc_h // 2

        # Calculator shadow
        shadow_offset = 8
        draw.rounded_rectangle([calc_x + shadow_offset, calc_y + shadow_offset,
                               calc_x + calc_w + shadow_offset, calc_y + calc_h + shadow_offset],
                              radius=24, fill=(0, 0, 0, 80))

        # Calculator body
        draw.rounded_rectangle([calc_x, calc_y, calc_x + calc_w, calc_y + calc_h],
                              radius=24, fill=(255, 255, 255, 255),
                              outline=(220, 220, 220, 255), width=4)

        # Display screen
        display_w, display_h = 280, 80
        display_x = calc_x + 20
        display_y = calc_y + 20
        draw.rounded_rectangle([display_x, display_y, display_x + display_w, display_y + display_h],
                              radius=12, fill=(44, 62, 80, 255))

        # Display text
        try:
            font_large = ImageFont.truetype("arial.ttf", 36)
        except:
            font_large = ImageFont.load_default()

        text = "123,456"
        bbox = draw.textbbox((0, 0), text, font=font_large)
        text_w = bbox[2] - bbox[0]
        text_h = bbox[3] - bbox[1]
        text_x = display_x + display_w - text_w - 15
        text_y = display_y + (display_h - text_h) // 2
        draw.text((text_x, text_y), text, fill=(0, 255, 136, 255), font=font_large)

        # Currency symbol
        try:
            font_small = ImageFont.truetype("arial.ttf", 20)
        except:
            font_small = ImageFont.load_default()

        currency = "د.ع"
        draw.text((display_x + 10, display_y + 10), currency, fill=(255, 255, 255, 200), font=font_small)

        # Calculator buttons (4x4 grid)
        button_size = 50
        button_margin = 15
        start_x = calc_x + 35
        start_y = calc_y + 130

        buttons = [
            ['C', '÷', '×', '⌫'],
            ['7', '8', '9', '-'],
            ['4', '5', '6', '+'],
            ['1', '2', '3', '='],
            ['0', '0', '.', '=']
        ]

        for row, button_row in enumerate(buttons[:4]):  # Only 4 rows
            for col, btn_text in enumerate(button_row):
                x = start_x + col * (button_size + button_margin)
                y = start_y + row * (button_size + button_margin)

                # Button colors
                if btn_text in ['C', '÷', '×', '⌫', '-', '+']:
                    color = (0, 123, 255, 255)
                    text_color = (255, 255, 255, 255)
                elif btn_text == '=':
                    color = (40, 167, 69, 255)
                    text_color = (255, 255, 255, 255)
                else:
                    color = (248, 249, 250, 255)
                    text_color = (73, 80, 87, 255)

                # Button shadow
                draw.ellipse([x + 3, y + 3, x + button_size + 3, y + button_size + 3],
                           fill=(0, 0, 0, 50))

                # Button
                draw.ellipse([x, y, x + button_size, y + button_size],
                           fill=color, outline=(200, 200, 200, 255), width=2)

                # Button text
                try:
                    btn_font = ImageFont.truetype("arial.ttf", 24)
                except:
                    btn_font = ImageFont.load_default()

                btn_bbox = draw.textbbox((0, 0), btn_text, font=btn_font)
                btn_text_w = btn_bbox[2] - btn_bbox[0]
                btn_text_h = btn_bbox[3] - btn_bbox[1]
                btn_text_x = x + (button_size - btn_text_w) // 2
                btn_text_y = y + (button_size - btn_text_h) // 2
                draw.text((btn_text_x, btn_text_y), btn_text, fill=text_color, font=btn_font)

        # Last row (0, ., =)
        # 0 button (double width)
        x = start_x
        y = start_y + 4 * (button_size + button_margin)
        double_width = button_size * 2 + button_margin

        draw.rounded_rectangle([x + 3, y + 3, x + double_width + 3, y + button_size + 3],
                              radius=25, fill=(0, 0, 0, 50))
        draw.rounded_rectangle([x, y, x + double_width, y + button_size],
                              radius=25, fill=(248, 249, 250, 255),
                              outline=(200, 200, 200, 255), width=2)

        # 0 text
        btn_bbox = draw.textbbox((0, 0), "0", font=btn_font)
        btn_text_w = btn_bbox[2] - btn_bbox[0]
        btn_text_h = btn_bbox[3] - btn_bbox[1]
        btn_text_x = x + (double_width - btn_text_w) // 2
        btn_text_y = y + (button_size - btn_text_h) // 2
        draw.text((btn_text_x, btn_text_y), "0", fill=(73, 80, 87, 255), font=btn_font)

        # . button
        x = start_x + 2 * (button_size + button_margin)
        draw.ellipse([x + 3, y + 3, x + button_size + 3, y + button_size + 3],
                   fill=(0, 0, 0, 50))
        draw.ellipse([x, y, x + button_size, y + button_size],
                   fill=(248, 249, 250, 255), outline=(200, 200, 200, 255), width=2)

        btn_bbox = draw.textbbox((0, 0), ".", font=btn_font)
        btn_text_w = btn_bbox[2] - btn_bbox[0]
        btn_text_h = btn_bbox[3] - btn_bbox[1]
        btn_text_x = x + (button_size - btn_text_w) // 2
        btn_text_y = y + (button_size - btn_text_h) // 2
        draw.text((btn_text_x, btn_text_y), ".", fill=(73, 80, 87, 255), font=btn_font)

        # = button
        x = start_x + 3 * (button_size + button_margin)
        draw.ellipse([x + 3, y + 3, x + button_size + 3, y + button_size + 3],
                   fill=(0, 0, 0, 50))
        draw.ellipse([x, y, x + button_size, y + button_size],
                   fill=(40, 167, 69, 255), outline=(200, 200, 200, 255), width=2)

        btn_bbox = draw.textbbox((0, 0), "=", font=btn_font)
        btn_text_w = btn_bbox[2] - btn_bbox[0]
        btn_text_h = btn_bbox[3] - btn_bbox[1]
        btn_text_x = x + (button_size - btn_text_w) // 2
        btn_text_y = y + (button_size - btn_text_h) // 2
        draw.text((btn_text_x, btn_text_y), "=", fill=(255, 255, 255, 255), font=btn_font)

        # Credit card
        card_x, card_y = calc_x + calc_w + 30, calc_y + 40
        card_w, card_h = 100, 64

        # Card shadow
        draw.rounded_rectangle([card_x + 5, card_y + 5, card_x + card_w + 5, card_y + card_h + 5],
                              radius=12, fill=(0, 0, 0, 80))

        # Card body
        draw.rounded_rectangle([card_x, card_y, card_x + card_w, card_y + card_h],
                              radius=12, fill=(76, 175, 80, 255),
                              outline=(255, 255, 255, 255), width=3)

        # Card stripe
        draw.rounded_rectangle([card_x + 8, card_y + 16, card_x + card_w - 8, card_y + 28],
                              radius=2, fill=(255, 255, 255, 200))

        # Card chip
        chip_size = 12
        chip_x = card_x + 15
        chip_y = card_y + 35
        draw.rounded_rectangle([chip_x, chip_y, chip_x + chip_size, chip_y + chip_size],
                              radius=2, fill=(255, 215, 0, 255),
                              outline=(255, 255, 255, 255), width=1)

        # Card dots
        for i in range(4):
            dot_x = card_x + 35 + i * 8
            dot_y = card_y + 40
            draw.ellipse([dot_x, dot_y, dot_x + 4, dot_y + 4], fill=(255, 255, 255, 255))

        # Coins
        coin_positions = [(calc_x + calc_w + 50, calc_y + 140, 32),
                         (calc_x + calc_w + 75, calc_y + 170, 28)]

        for coin_x, coin_y, coin_r in coin_positions:
            # Coin shadow
            draw.ellipse([coin_x + 4, coin_y + 4, coin_x + coin_r + 4, coin_y + coin_r + 4],
                       fill=(0, 0, 0, 80))

            # Coin gradient effect
            for i in range(coin_r // 2):
                alpha = 255 - int(i * 3)
                if alpha < 180:
                    alpha = 180
                color = (255, 215 - i, 0, alpha)
                draw.ellipse([coin_x + i, coin_y + i, coin_x + coin_r - i, coin_y + coin_r - i],
                           fill=color)

            # Coin border
            draw.ellipse([coin_x, coin_y, coin_x + coin_r, coin_y + coin_r],
                       outline=(255, 255, 255, 255), width=3)

            # Coin text
            try:
                coin_font = ImageFont.truetype("arial.ttf", 14)
            except:
                coin_font = ImageFont.load_default()

            coin_text = "د.ع"
            coin_bbox = draw.textbbox((0, 0), coin_text, font=coin_font)
            coin_text_w = coin_bbox[2] - coin_bbox[0]
            coin_text_h = coin_bbox[3] - coin_bbox[1]
            coin_text_x = coin_x + (coin_r - coin_text_w) // 2
            coin_text_y = coin_y + (coin_r - coin_text_h) // 2
            draw.text((coin_text_x, coin_text_y), coin_text, fill=(139, 69, 19, 255), font=coin_font)

        # Title at bottom
        try:
            title_font = ImageFont.truetype("arial.ttf", 48)
            subtitle_font = ImageFont.truetype("arial.ttf", 32)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()

        title_text = "محاسب ديون"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_w = title_bbox[2] - title_bbox[0]
        title_x = center - title_w // 2
        title_y = center + 280

        # Title shadow
        draw.text((title_x + 3, title_y + 3), title_text, fill=(0, 0, 0, 100), font=title_font)
        # Title text
        draw.text((title_x, title_y), title_text, fill=(255, 255, 255, 255), font=title_font)

        subtitle_text = "احترافي"
        subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
        subtitle_w = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = center - subtitle_w // 2
        subtitle_y = title_y + 60

        # Subtitle shadow
        draw.text((subtitle_x + 2, subtitle_y + 2), subtitle_text, fill=(0, 0, 0, 100), font=subtitle_font)
        # Subtitle text
        draw.text((subtitle_x, subtitle_y), subtitle_text, fill=(255, 255, 255, 230), font=subtitle_font)

        # Save high quality icon
        img.save('assets/icons/app_icon.png', 'PNG', quality=100)
        print("✅ High quality app icon created successfully!")

        # Create different sizes
        sizes = [512, 192, 144, 96, 72, 48, 36]
        for s in sizes:
            resized = img.resize((s, s), Image.Resampling.LANCZOS)
            resized.save(f'assets/icons/app_icon_{s}.png', 'PNG', quality=100)
            print(f"✅ Created {s}x{s} icon")

        return True

    if __name__ == "__main__":
        create_professional_icon()

except ImportError:
    print("❌ PIL not available. Creating enhanced SVG icon...")

    # Create enhanced SVG icon
    svg_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3c72;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#2a5298;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3d6cb9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45a049;stop-opacity:1" />
    </linearGradient>
    <radialGradient id="coinGradient" cx="30%" cy="30%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFA500;stop-opacity:1" />
    </radialGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#bgGradient)" stroke="#ffffff" stroke-width="8"/>

  <!-- Calculator body -->
  <rect x="140" y="120" width="232" height="280" rx="16" ry="16" fill="#ffffff" stroke="#e0e0e0" stroke-width="2"/>

  <!-- Display screen -->
  <rect x="160" y="140" width="192" height="60" rx="8" ry="8" fill="#2c3e50"/>

  <!-- Display text -->
  <text x="330" y="175" text-anchor="end" fill="#00ff88" font-family="Arial, sans-serif" font-size="24" font-weight="bold">123,456</text>
  <text x="170" y="165" text-anchor="start" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" opacity="0.8">د.ع</text>

  <!-- Calculator buttons -->
  <!-- Row 1 -->
  <circle cx="180" cy="240" r="18" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
  <text x="180" y="246" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">C</text>

  <circle cx="220" cy="240" r="18" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
  <text x="220" y="246" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">÷</text>

  <circle cx="260" cy="240" r="18" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
  <text x="260" y="246" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">×</text>

  <circle cx="300" cy="240" r="18" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
  <text x="300" y="246" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">⌫</text>

  <!-- Row 2 -->
  <circle cx="180" cy="280" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="180" y="286" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">7</text>

  <circle cx="220" cy="280" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="220" y="286" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">8</text>

  <circle cx="260" cy="280" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="260" y="286" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">9</text>

  <circle cx="300" cy="280" r="18" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
  <text x="300" y="286" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">-</text>

  <!-- Row 3 -->
  <circle cx="180" cy="320" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="180" y="326" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">4</text>

  <circle cx="220" cy="320" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="220" y="326" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">5</text>

  <circle cx="260" cy="320" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="260" y="326" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">6</text>

  <circle cx="300" cy="320" r="18" fill="#007bff" stroke="#0056b3" stroke-width="1"/>
  <text x="300" y="326" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">+</text>

  <!-- Row 4 -->
  <circle cx="180" cy="360" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="180" y="366" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">1</text>

  <circle cx="220" cy="360" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="220" y="366" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">2</text>

  <circle cx="260" cy="360" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="260" y="366" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">3</text>

  <circle cx="300" cy="360" r="18" fill="#28a745" stroke="#1e7e34" stroke-width="1"/>
  <text x="300" y="366" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="14" font-weight="bold">=</text>

  <!-- Row 5 -->
  <ellipse cx="200" cy="400" rx="28" ry="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="200" y="406" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">0</text>

  <circle cx="260" cy="400" r="18" fill="#f8f9fa" stroke="#dee2e6" stroke-width="1"/>
  <text x="260" y="406" text-anchor="middle" fill="#495057" font-family="Arial, sans-serif" font-size="14" font-weight="bold">.</text>

  <!-- Credit card -->
  <rect x="390" y="140" width="80" height="50" rx="8" ry="8" fill="url(#cardGradient)" stroke="#ffffff" stroke-width="2"/>
  <rect x="398" y="155" width="64" height="8" rx="2" ry="2" fill="#ffffff" opacity="0.8"/>
  <rect x="405" y="170" width="12" height="8" rx="2" ry="2" fill="#FFD700" stroke="#ffffff" stroke-width="1"/>
  <circle cx="420" cy="178" r="2" fill="#ffffff"/>
  <circle cx="430" cy="178" r="2" fill="#ffffff"/>
  <circle cx="440" cy="178" r="2" fill="#ffffff"/>
  <circle cx="450" cy="178" r="2" fill="#ffffff"/>

  <!-- Coins -->
  <circle cx="400" cy="220" r="20" fill="url(#coinGradient)" stroke="#ffffff" stroke-width="2"/>
  <text x="400" y="226" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="10" font-weight="bold">د.ع</text>

  <circle cx="430" cy="250" r="16" fill="url(#coinGradient)" stroke="#ffffff" stroke-width="2" opacity="0.9"/>
  <text x="430" y="255" text-anchor="middle" fill="#8B4513" font-family="Arial, sans-serif" font-size="8" font-weight="bold">د.ع</text>

  <!-- Title -->
  <text x="256" y="460" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="32" font-weight="bold">محاسب ديون</text>
  <text x="256" y="490" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="20" opacity="0.9">احترافي</text>
</svg>'''

    with open('assets/icons/app_icon.svg', 'w', encoding='utf-8') as f:
        f.write(svg_content)
    print("✅ Enhanced SVG icon created!")

    # Also create a simple PNG fallback
    print("Creating simple PNG fallback...")
    import subprocess
    try:
        # Try to convert SVG to PNG using system tools
        subprocess.run(['magick', 'assets/icons/app_icon.svg', 'assets/icons/app_icon.png'], check=True)
        print("✅ PNG icon created from SVG!")
    except:
        print("⚠️ Could not convert SVG to PNG. Please use the SVG file or convert manually.")