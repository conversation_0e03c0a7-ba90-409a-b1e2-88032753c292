@echo off
echo 🏃‍♂️ تسريع المحاكي - Emulator Performance Boost
echo ===============================================

echo.
echo 🔧 تحسين إعدادات المحاكي...

echo.
echo اختر نوع التحسين:
echo [1] تشغيل سريع على المحاكي (مستحسن)
echo [2] تشغيل مع تحسينات الرسوميات
echo [3] تشغيل مع تقليل استهلاك الذاكرة
echo [4] تشغيل Profile Mode (الأسرع)

set /p choice="اختر رقم (1-4): "

echo.
echo 🧹 تنظيف الملفات المؤقتة...
if exist "build" rmdir /s /q "build"
flutter clean
flutter pub get

echo.
if "%choice%"=="1" (
    echo 🚀 تشغيل سريع على المحاكي...
    flutter run --hot --enable-software-rendering --disable-service-auth-codes
) else if "%choice%"=="2" (
    echo 🎨 تشغيل مع تحسينات الرسوميات...
    flutter run --hot --enable-software-rendering --enable-impeller
) else if "%choice%"=="3" (
    echo 💾 تشغيل مع تقليل استهلاك الذاكرة...
    flutter run --hot --enable-software-rendering --dart-define=flutter.inspector.structuredErrors=false
) else if "%choice%"=="4" (
    echo ⚡ تشغيل Profile Mode (الأسرع)...
    flutter run --profile --enable-software-rendering
) else (
    echo خيار غير صحيح، تشغيل عادي...
    flutter run --hot --enable-software-rendering
)

echo.
echo ✅ تم الانتهاء!
echo 💡 نصائح لتحسين الأداء:
echo    - استخدم Hot Reload بدلاً من Hot Restart
echo    - أغلق التطبيقات الأخرى أثناء التطوير
echo    - استخدم Profile Mode للاختبار النهائي
pause
