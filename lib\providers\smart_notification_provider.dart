import 'package:flutter/foundation.dart';
import '../models/notification_model.dart';
import '../models/debt.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../utils/number_formatter.dart';

class SmartNotificationProvider with ChangeNotifier {
  List<NotificationModel> _notifications = [];
  bool _isLoading = false;

  List<NotificationModel> get notifications => _notifications;
  bool get isLoading => _isLoading;

  // الحصول على التنبيهات غير المقروءة
  List<NotificationModel> get unreadNotifications =>
      _notifications.where((n) => !n.isRead).toList();

  // عدد التنبيهات غير المقروءة
  int get unreadCount => unreadNotifications.length;

  // الحصول على التنبيهات حسب النوع
  List<NotificationModel> getNotificationsByType(NotificationType type) {
    return _notifications.where((n) => n.type == type).toList();
  }

  // الحصول على التنبيهات حسب العميل
  List<NotificationModel> getNotificationsByCustomer(String customerId) {
    return _notifications.where((n) => n.customerId == customerId).toList();
  }

  // إنشاء التنبيهات الذكية
  Future<void> generateSmartNotifications({
    required DebtProvider debtProvider,
    required CustomerProvider customerProvider,
    required CardInventoryProvider inventoryProvider,
  }) async {
    _isLoading = true;
    notifyListeners();

    try {
      // مسح التنبيهات القديمة
      _notifications.clear();

      // إنشاء تنبيهات الديون
      await _generateDebtNotifications(debtProvider, customerProvider);

      // إنشاء تنبيهات المخزون
      await _generateInventoryNotifications(inventoryProvider);

      // ترتيب التنبيهات حسب الأولوية والتاريخ
      _sortNotifications();
    } catch (e) {
      debugPrint('خطأ في إنشاء التنبيهات: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // إنشاء تنبيهات الديون
  Future<void> _generateDebtNotifications(
    DebtProvider debtProvider,
    CustomerProvider customerProvider,
  ) async {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    for (final debt in debtProvider.debts) {
      if (debt.status == DebtStatus.paid) continue;

      final customer = customerProvider.customers.firstWhere(
        (c) => c.id == debt.customerId,
      );

      final dueDate = DateTime(
        debt.dueDate.year,
        debt.dueDate.month,
        debt.dueDate.day,
      );

      final daysDifference = dueDate.difference(today).inDays;

      // تنبيهات الديون المتأخرة
      if (daysDifference < 0) {
        _addNotification(
          NotificationModel(
            id: 'overdue_${debt.id}',
            title: 'دين متأخر',
            message:
                'دين ${customer.name} متأخر ${-daysDifference} يوم - ${NumberFormatter.formatCurrency(debt.remainingAmount)}',
            type: NotificationType.overdue,
            priority: NotificationPriority.urgent,
            createdAt: now,
            debtId: debt.id?.toString(),
            customerId: customer.id?.toString(),
            customerName: customer.name,
            amount: debt.remainingAmount,
            dueDate: debt.dueDate,
            additionalData: {
              'createdAt': debt.entryDate.toIso8601String(),
              'notes': debt.notes ?? '',
              'cardType': debt.cardType,
              'daysDifference': daysDifference,
            },
          ),
        );
      }
      // تنبيهات الديون المستحقة اليوم
      else if (daysDifference == 0) {
        _addNotification(
          NotificationModel(
            id: 'due_today_${debt.id}',
            title: 'دين مستحق اليوم',
            message:
                'دين ${customer.name} مستحق اليوم - ${NumberFormatter.formatCurrency(debt.remainingAmount)}',
            type: NotificationType.dueToday,
            priority: NotificationPriority.high,
            createdAt: now,
            debtId: debt.id?.toString(),
            customerId: customer.id?.toString(),
            customerName: customer.name,
            amount: debt.remainingAmount,
            dueDate: debt.dueDate,
            additionalData: {
              'createdAt': debt.entryDate.toIso8601String(),
              'notes': debt.notes ?? '',
              'cardType': debt.cardType,
              'daysDifference': daysDifference,
            },
          ),
        );
      }
      // تنبيهات الديون المستحقة قريباً (خلال 3 أيام)
      else if (daysDifference <= 3) {
        _addNotification(
          NotificationModel(
            id: 'due_soon_${debt.id}',
            title: 'دين مستحق قريباً',
            message:
                'دين ${customer.name} مستحق خلال $daysDifference أيام - ${NumberFormatter.formatCurrency(debt.remainingAmount)}',
            type: NotificationType.dueSoon,
            priority: NotificationPriority.medium,
            createdAt: now,
            debtId: debt.id?.toString(),
            customerId: customer.id?.toString(),
            customerName: customer.name,
            amount: debt.remainingAmount,
            dueDate: debt.dueDate,
            additionalData: {
              'createdAt': debt.entryDate.toIso8601String(),
              'notes': debt.notes ?? '',
              'cardType': debt.cardType,
              'daysDifference': daysDifference,
            },
          ),
        );
      }
    }
  }

  // إنشاء تنبيهات المخزون
  Future<void> _generateInventoryNotifications(
    CardInventoryProvider inventoryProvider,
  ) async {
    final now = DateTime.now();

    for (final inventory in inventoryProvider.inventories) {
      // تنبيهات نفاد المخزون
      if (inventory.isOutOfStock) {
        _addNotification(
          NotificationModel(
            id: 'out_of_stock_${inventory.id}',
            title: 'نفاد المخزون',
            message:
                'نفد مخزون ${inventory.cardType} - الكمية: ${inventory.quantity}',
            type: NotificationType.outOfStock,
            priority: NotificationPriority.urgent,
            createdAt: now,
            additionalData: {
              'cardType': inventory.cardType,
              'quantity': inventory.quantity,
            },
          ),
        );
      }
      // تنبيهات المخزون المنخفض
      else if (inventory.isLowStock) {
        _addNotification(
          NotificationModel(
            id: 'low_stock_${inventory.id}',
            title: 'مخزون منخفض',
            message:
                'مخزون ${inventory.cardType} منخفض - الكمية: ${inventory.quantity}',
            type: NotificationType.lowStock,
            priority: NotificationPriority.high,
            createdAt: now,
            additionalData: {
              'cardType': inventory.cardType,
              'quantity': inventory.quantity,
              'minQuantity': inventory.minQuantity,
            },
          ),
        );
      }
    }
  }

  // إضافة تنبيه
  void _addNotification(NotificationModel notification) {
    // تجنب التكرار
    if (!_notifications.any((n) => n.id == notification.id)) {
      _notifications.add(notification);
      notifyListeners(); // إشعار المستمعين بالتحديث
    }
  }

  // إضافة تنبيه من خدمة خارجية
  void addExternalNotification(NotificationModel notification) {
    _addNotification(notification);
  }

  // ترتيب التنبيهات
  void _sortNotifications() {
    _notifications.sort((a, b) {
      // ترتيب حسب الأولوية أولاً
      final priorityComparison = _getPriorityValue(
        b.priority,
      ).compareTo(_getPriorityValue(a.priority));

      if (priorityComparison != 0) return priorityComparison;

      // ثم حسب التاريخ (الأحدث أولاً)
      return b.createdAt.compareTo(a.createdAt);
    });
  }

  // الحصول على قيمة الأولوية للترتيب
  int _getPriorityValue(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.urgent:
        return 4;
      case NotificationPriority.high:
        return 3;
      case NotificationPriority.medium:
        return 2;
      case NotificationPriority.low:
        return 1;
    }
  }

  // تحديد التنبيه كمقروء
  void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      notifyListeners();
    }
  }

  // تحديد جميع التنبيهات كمقروءة
  void markAllAsRead() {
    _notifications = _notifications
        .map((n) => n.copyWith(isRead: true))
        .toList();
    notifyListeners();
  }

  // حذف تنبيه
  void removeNotification(String notificationId) {
    _notifications.removeWhere((n) => n.id == notificationId);
    notifyListeners();
  }

  // مسح جميع التنبيهات
  void clearAllNotifications() {
    _notifications.clear();
    notifyListeners();
  }

  // الحصول على إحصائيات التنبيهات
  Map<String, int> getNotificationStats() {
    return {
      'total': _notifications.length,
      'unread': unreadCount,
      'overdue': getNotificationsByType(NotificationType.overdue).length,
      'dueToday': getNotificationsByType(NotificationType.dueToday).length,
      'dueSoon': getNotificationsByType(NotificationType.dueSoon).length,
      'lowStock': getNotificationsByType(NotificationType.lowStock).length,
      'outOfStock': getNotificationsByType(NotificationType.outOfStock).length,
    };
  }
}
