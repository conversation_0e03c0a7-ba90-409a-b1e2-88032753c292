import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/smart_notification_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../models/notification_model.dart';
import '../utils/number_formatter.dart';
import 'package:intl/intl.dart';

class SmartNotificationsScreen extends StatefulWidget {
  const SmartNotificationsScreen({super.key});

  @override
  State<SmartNotificationsScreen> createState() =>
      _SmartNotificationsScreenState();
}

class _SmartNotificationsScreenState extends State<SmartNotificationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  NotificationType? _selectedFilter;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotifications() async {
    final smartNotificationProvider = Provider.of<SmartNotificationProvider>(
      context,
      listen: false,
    );
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final inventoryProvider = Provider.of<CardInventoryProvider>(
      context,
      listen: false,
    );

    await smartNotificationProvider.generateSmartNotifications(
      debtProvider: debtProvider,
      customerProvider: customerProvider,
      inventoryProvider: inventoryProvider,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'التنبيهات الذكية',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          Consumer<SmartNotificationProvider>(
            builder: (context, provider, _) {
              if (provider.unreadCount > 0) {
                return Stack(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.mark_email_read),
                      onPressed: () => provider.markAllAsRead(),
                      tooltip: 'تحديد الكل كمقروء',
                    ),
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '${provider.unreadCount}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadNotifications,
            tooltip: 'تحديث',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.notifications, size: 20)),
            Tab(
              text: 'الديون',
              icon: Icon(Icons.account_balance_wallet, size: 20),
            ),
            Tab(text: 'المخزون', icon: Icon(Icons.inventory, size: 20)),
          ],
        ),
      ),
      body: Column(
        children: [
          _buildStatsCard(),
          _buildFilterChips(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllNotifications(),
                _buildDebtNotifications(),
                _buildInventoryNotifications(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadNotifications,
        backgroundColor: Colors.blue.shade600,
        child: const Icon(Icons.refresh, color: Colors.white),
      ),
    );
  }

  Widget _buildStatsCard() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, provider, _) {
        final stats = provider.getNotificationStats();

        return Container(
          margin: const EdgeInsets.all(12),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade600, Colors.blue.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.2),
                blurRadius: 6,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem(
                    'المجموع',
                    stats['total']!,
                    Icons.notifications,
                  ),
                  _buildStatItem(
                    'غير مقروء',
                    stats['unread']!,
                    Icons.mark_email_unread,
                  ),
                  _buildStatItem('عاجل', stats['overdue']!, Icons.warning),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('اليوم', stats['dueToday']!, Icons.today),
                  _buildStatItem('قريباً', stats['dueSoon']!, Icons.schedule),
                  _buildStatItem(
                    'مخزون',
                    stats['lowStock']! + stats['outOfStock']!,
                    Icons.inventory,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(String label, int count, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Colors.white, size: 18),
        const SizedBox(height: 2),
        Text(
          count.toString(),
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 10),
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    final filters = [
      {'type': null, 'label': 'الكل', 'icon': Icons.all_inclusive},
      {
        'type': NotificationType.overdue,
        'label': 'متأخر',
        'icon': Icons.warning,
      },
      {
        'type': NotificationType.dueToday,
        'label': 'اليوم',
        'icon': Icons.today,
      },
      {
        'type': NotificationType.dueSoon,
        'label': 'قريباً',
        'icon': Icons.schedule,
      },
      {
        'type': NotificationType.lowStock,
        'label': 'مخزون منخفض',
        'icon': Icons.inventory_2,
      },
      {
        'type': NotificationType.outOfStock,
        'label': 'نافد',
        'icon': Icons.remove_shopping_cart,
      },
    ];

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: filters.length,
        itemBuilder: (context, index) {
          final filter = filters[index];
          final isSelected = _selectedFilter == filter['type'];

          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              selected: isSelected,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.blue.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(filter['label'] as String),
                ],
              ),
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = selected
                      ? filter['type'] as NotificationType?
                      : null;
                });
              },
              selectedColor: Colors.blue.shade600,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.blue.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAllNotifications() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, provider, _) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        var notifications = provider.notifications;

        if (_selectedFilter != null) {
          notifications = provider.getNotificationsByType(_selectedFilter!);
        }

        if (notifications.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: _loadNotifications,
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              return _buildNotificationCard(notifications[index], provider);
            },
          ),
        );
      },
    );
  }

  Widget _buildDebtNotifications() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, provider, _) {
        final debtNotifications = provider.notifications
            .where(
              (n) =>
                  n.type == NotificationType.overdue ||
                  n.type == NotificationType.dueToday ||
                  n.type == NotificationType.dueSoon,
            )
            .toList();

        if (debtNotifications.isEmpty) {
          return _buildEmptyState(message: 'لا توجد تنبيهات ديون');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: debtNotifications.length,
          itemBuilder: (context, index) {
            return _buildNotificationCard(debtNotifications[index], provider);
          },
        );
      },
    );
  }

  Widget _buildInventoryNotifications() {
    return Consumer<SmartNotificationProvider>(
      builder: (context, provider, _) {
        final inventoryNotifications = provider.notifications
            .where(
              (n) =>
                  n.type == NotificationType.lowStock ||
                  n.type == NotificationType.outOfStock,
            )
            .toList();

        if (inventoryNotifications.isEmpty) {
          return _buildEmptyState(message: 'لا توجد تنبيهات مخزون');
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: inventoryNotifications.length,
          itemBuilder: (context, index) {
            return _buildNotificationCard(
              inventoryNotifications[index],
              provider,
            );
          },
        );
      },
    );
  }

  Widget _buildNotificationCard(
    NotificationModel notification,
    SmartNotificationProvider provider,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: notification.isRead ? 1 : 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          if (!notification.isRead) {
            provider.markAsRead(notification.id);
          }
          _showNotificationDetails(notification);
        },
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: notification.isRead
                ? null
                : Border.all(
                    color: _getPriorityColor(notification.priority),
                    width: 2,
                  ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: _getTypeColor(
                        notification.type,
                      ).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      notification.type.icon,
                      style: const TextStyle(fontSize: 16),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                notification.title,
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: notification.isRead
                                      ? Colors.grey.shade600
                                      : Colors.black87,
                                ),
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: _getPriorityColor(notification.priority),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                notification.priority.displayName,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          notification.message,
                          style: TextStyle(
                            fontSize: 12,
                            color: notification.isRead
                                ? Colors.grey.shade500
                                : Colors.grey.shade700,
                          ),
                        ),
                        const SizedBox(height: 8),
                        // معلومات إضافية للديون
                        if (notification.type == NotificationType.overdue ||
                            notification.type == NotificationType.dueToday ||
                            notification.type == NotificationType.dueSoon)
                          _buildDebtDetails(notification),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 14,
                    color: Colors.grey.shade500,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDateTime(notification.createdAt),
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
                  ),
                  const Spacer(),
                  if (!notification.isRead)
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        shape: BoxShape.circle,
                      ),
                    ),
                  const SizedBox(width: 8),
                  PopupMenuButton<String>(
                    icon: Icon(Icons.more_vert, color: Colors.grey.shade600),
                    onSelected: (value) {
                      switch (value) {
                        case 'mark_read':
                          provider.markAsRead(notification.id);
                          break;
                        case 'delete':
                          provider.removeNotification(notification.id);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (!notification.isRead)
                        const PopupMenuItem(
                          value: 'mark_read',
                          child: Row(
                            children: [
                              Icon(Icons.mark_email_read, size: 16),
                              SizedBox(width: 8),
                              Text('تحديد كمقروء'),
                            ],
                          ),
                        ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState({String? message}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.notifications_off, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            message ?? 'لا توجد تنبيهات',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اسحب للأسفل للتحديث',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Text(notification.type.icon, style: const TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            Expanded(child: Text(notification.title)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            const SizedBox(height: 16),
            if (notification.customerName != null) ...[
              _buildDetailRow('العميل', notification.customerName!),
              const SizedBox(height: 8),
            ],
            if (notification.amount != null) ...[
              _buildDetailRow(
                'المبلغ',
                NumberFormatter.formatCurrency(notification.amount!),
              ),
              const SizedBox(height: 8),
            ],
            if (notification.dueDate != null) ...[
              _buildDetailRow(
                'تاريخ الاستحقاق',
                DateFormat('yyyy/MM/dd').format(notification.dueDate!),
              ),
              const SizedBox(height: 8),
            ],
            _buildDetailRow('الأولوية', notification.priority.displayName),
            const SizedBox(height: 8),
            _buildDetailRow('التاريخ', _formatDateTime(notification.createdAt)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            '$label:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
        ),
        Expanded(
          child: Text(value, style: const TextStyle(color: Colors.black87)),
        ),
      ],
    );
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.overdue:
        return Colors.red;
      case NotificationType.dueToday:
        return Colors.orange;
      case NotificationType.dueSoon:
        return Colors.blue;
      case NotificationType.lowStock:
        return Colors.orange;
      case NotificationType.outOfStock:
        return Colors.red;
      case NotificationType.payment:
        return Colors.green;
      case NotificationType.general:
        return Colors.grey;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.urgent:
        return Colors.red;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.medium:
        return Colors.blue;
      case NotificationPriority.low:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays == 0) {
      return 'اليوم ${DateFormat('HH:mm').format(dateTime)}';
    } else if (difference.inDays == 1) {
      return 'أمس ${DateFormat('HH:mm').format(dateTime)}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} أيام ${DateFormat('HH:mm').format(dateTime)}';
    } else {
      return DateFormat('yyyy/MM/dd HH:mm').format(dateTime);
    }
  }

  // بناء تفاصيل الدين
  Widget _buildDebtDetails(NotificationModel notification) {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // تاريخ القيد
          if (notification.additionalData?['createdAt'] != null) ...[
            _buildSimpleInfoRow(
              icon: Icons.add_circle,
              iconColor: Colors.blue.shade600,
              label: 'تاريخ القيد',
              value: _formatFullDate(
                DateTime.parse(notification.additionalData!['createdAt']),
              ),
              extra:
                  'منذ ${_calculateDaysSince(DateTime.parse(notification.additionalData!['createdAt']))} يوم',
            ),
            const SizedBox(height: 8),
          ],

          // تاريخ الاستحقاق
          if (notification.dueDate != null) ...[
            _buildSimpleInfoRow(
              icon: Icons.event,
              iconColor: Colors.red.shade600,
              label: 'تاريخ الاستحقاق',
              value: _formatFullDate(notification.dueDate!),
              extra: _calculateDaysUntil(notification.dueDate!) < 0
                  ? 'متأخر ${-_calculateDaysUntil(notification.dueDate!)} يوم'
                  : _calculateDaysUntil(notification.dueDate!) == 0
                  ? 'مستحق اليوم'
                  : 'باقي ${_calculateDaysUntil(notification.dueDate!)} يوم',
            ),
            const SizedBox(height: 8),
          ],

          // نوع البطاقة
          if (notification.additionalData?['cardType'] != null) ...[
            _buildSimpleInfoRow(
              icon: Icons.credit_card,
              iconColor: Colors.green.shade600,
              label: 'نوع البطاقة',
              value: notification.additionalData!['cardType'].toString(),
            ),
            const SizedBox(height: 8),
          ],

          // الملاحظات
          if (notification.additionalData?['notes'] != null &&
              notification.additionalData!['notes'].toString().isNotEmpty) ...[
            _buildSimpleInfoRow(
              icon: Icons.note,
              iconColor: Colors.orange.shade600,
              label: 'الملاحظات',
              value: notification.additionalData!['notes'].toString(),
            ),
          ],
        ],
      ),
    );
  }

  // بناء صف معلومة بسيط
  Widget _buildSimpleInfoRow({
    required IconData icon,
    required Color iconColor,
    required String label,
    required String value,
    String? extra,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 16, color: iconColor),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$label: $value',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey.shade800,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (extra != null) ...[
                const SizedBox(height: 2),
                Text(
                  extra,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // تنسيق التاريخ بالأرقام فقط
  String _formatFullDate(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day}';
  }

  // حساب عدد الأيام منذ تاريخ معين
  int _calculateDaysSince(DateTime fromDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final compareDate = DateTime(fromDate.year, fromDate.month, fromDate.day);

    return today.difference(compareDate).inDays;
  }

  // حساب عدد الأيام حتى تاريخ معين
  int _calculateDaysUntil(DateTime toDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final compareDate = DateTime(toDate.year, toDate.month, toDate.day);

    return compareDate.difference(today).inDays;
  }
}
