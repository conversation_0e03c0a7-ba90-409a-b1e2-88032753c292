import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/custom_card_type.dart';

import '../providers/debt_provider.dart';
import '../providers/customer_provider.dart';
import '../providers/card_type_provider.dart';
import '../providers/card_inventory_provider.dart';
import '../providers/form_data_provider.dart';

import '../widgets/animated_checkmark_widget.dart';

class AddDebtBottomSheet extends StatefulWidget {
  const AddDebtBottomSheet({super.key, this.customer});
  final Customer? customer;

  @override
  State<AddDebtBottomSheet> createState() => _AddDebtBottomSheetState();
}

class _AddDebtBottomSheetState extends State<AddDebtBottomSheet>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _customerNameController = TextEditingController();
  final _cardTypeController = TextEditingController();
  final _quantityController = TextEditingController();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  final _newCardTypeController = TextEditingController();
  final _cardTypeNotesController = TextEditingController();

  // Quick values are now managed by FormDataProvider

  final _newQuantityController = TextEditingController();
  final _newAmountController = TextEditingController();
  final _newNoteController = TextEditingController();

  // Quick notes are now managed by FormDataProvider

  Customer? _selectedCustomer;
  String? _selectedCardType;
  DateTime _entryDate = DateTime.now();
  DateTime _dueDate = () {
    final now = DateTime.now();
    // إضافة شهر كامل بدلاً من 7 أيام
    final future = DateTime(now.year, now.month + 1, now.day);
    return DateTime(future.year, future.month, future.day, 23, 59);
  }();
  bool _isLoading = false;

  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    _animationController.forward();

    if (widget.customer != null) {
      _selectedCustomer = widget.customer;
      _customerNameController.text = widget.customer!.name;
    }

    _loadFormData();

    // Initialize card type after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );
      cardTypeProvider.loadCustomCardTypes().then((_) {
        if (mounted) {
          setState(() {
            // Don't set a default card type - user must choose
            if (_selectedCardType != null &&
                !cardTypeProvider.allCardTypes.any(
                  (opt) => opt.id == _selectedCardType,
                )) {
              // If the selected type is not found, clear it
              _selectedCardType = null;
            }
          });
        }
      });
    });
  }

  void _loadFormData() async {
    final formDataProvider = Provider.of<FormDataProvider>(
      context,
      listen: false,
    );
    await formDataProvider.loadFormData();

    if (mounted && formDataProvider.isLoaded) {
      setState(() {
        _quantityController.text = formDataProvider.lastQuantity;
        _amountController.text = formDataProvider.lastAmount;
        _notesController.text = formDataProvider.lastNotes;
      });
    }
  }

  void _saveFormData() {
    final formDataProvider = Provider.of<FormDataProvider>(
      context,
      listen: false,
    );
    formDataProvider.saveFormData(
      quantity: _quantityController.text,
      amount: _amountController.text,
      notes: _notesController.text,
    );
  }

  @override
  void dispose() {
    // Save values before disposing
    _saveFormData();

    _animationController.dispose();
    _customerNameController.dispose();
    _cardTypeController.dispose();
    _quantityController.dispose();
    _amountController.dispose();
    _notesController.dispose();
    _newCardTypeController.dispose();
    _cardTypeNotesController.dispose();
    _newQuantityController.dispose();
    _newAmountController.dispose();
    _newNoteController.dispose();
    super.dispose();
  }

  Future<void> _saveDebt() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedCustomer == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار عميل أولاً'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_selectedCardType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار نوع الكارت'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      // Remove commas from amount before parsing
      final cleanAmount = _amountController.text.replaceAll(',', '');

      final quantity = int.parse(_quantityController.text);
      final enteredAmount = double.parse(cleanAmount);

      // Don't multiply - use the amount as entered by user
      final finalAmount = enteredAmount;

      // تحويل نوع البطاقة إلى الاسم الفعلي للمخزون
      final cardTypeProvider = Provider.of<CardTypeProvider>(
        context,
        listen: false,
      );
      String actualCardTypeName = _selectedCardType!;

      // إذا كان نوع مخصص، احصل على الاسم الفعلي
      if (_selectedCardType!.startsWith('custom_')) {
        final cardTypeId = int.parse(
          _selectedCardType!.replaceFirst('custom_', ''),
        );
        final customCardType = cardTypeProvider.customCardTypes.firstWhere(
          (ct) => ct.id == cardTypeId,
          orElse: () => throw Exception('Card type not found'),
        );
        actualCardTypeName = customCardType.displayName;
      }

      final debt = Debt(
        customerId: _selectedCustomer!.id!,
        itemName: 'عنصر', // Default item name since field is removed
        quantity: quantity,
        amount: finalAmount, // Use amount as entered by user
        cardType: _selectedCardType!, // Custom card type ID as string
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        entryDate: _entryDate,
        dueDate: _dueDate,
        createdAt: now,
        updatedAt: now,
      );

      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      final cardInventoryProvider = Provider.of<CardInventoryProvider>(
        context,
        listen: false,
      );

      // استقطاع ذكي - فقط للبطاقات (ليس النقدي) وبدون رسائل
      if (actualCardTypeName != 'نقدي' &&
          actualCardTypeName.toLowerCase() != 'cash') {
        final currentStock = cardInventoryProvider.getStockQuantity(
          actualCardTypeName,
        );

        if (currentStock >= quantity) {
          await cardInventoryProvider.deductStock(actualCardTypeName, quantity);
        }
      }

      // حفظ الدين
      await debtProvider.addDebt(debt);

      // Save current values for next time
      _saveFormData();

      // Refresh debts if we're viewing this customer's debts
      if (debtProvider.currentCustomerId == debt.customerId) {
        await debtProvider.refreshCurrentCustomerDebts();
      }

      if (mounted) {
        // إغلاق النافذة أولاً
        Navigator.pop(context);

        // عرض علامة صح متحركة مع تأثير جميل
        _showAnimatedCheckmark(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // عرض علامة صح متحركة مع تأثير جميل
  void _showAnimatedCheckmark(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
      builder: (BuildContext context) {
        return const AnimatedCheckmarkWidget();
      },
    );
  }

  Future<void> _selectDate(BuildContext context, bool isEntryDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isEntryDate ? _entryDate : _dueDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      locale: const Locale('ar', 'SA'), // تعيين اللغة العربية
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Colors.blue.shade600,
              onSurface: Colors.black87,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Colors.blue.shade600,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      if (isEntryDate) {
        if (!mounted) return;
        final TimeOfDay? time = await showTimePicker(
          context: mounted ? context : context,
          initialTime: TimeOfDay.fromDateTime(_entryDate),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: ColorScheme.light(
                  primary: Colors.blue.shade600,
                  onSurface: Colors.black87,
                ),
              ),
              child: child!,
            );
          },
        );

        if (time != null) {
          setState(() {
            _entryDate = DateTime(
              picked.year,
              picked.month,
              picked.day,
              time.hour,
              time.minute,
            );
          });
        } else {
          setState(() {
            _entryDate = picked;
          });
        }
      } else {
        setState(() {
          // تعيين تاريخ الاستحقاق مع وقت افتراضي (نهاية اليوم)
          _dueDate = DateTime(picked.year, picked.month, picked.day, 23, 59);
        });
      }
    }
  }

  void _showCustomerSelectionDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _CustomerSelectionBottomSheet(
        onCustomerSelected: (customer) {
          setState(() {
            _selectedCustomer = customer;
            _customerNameController.text = customer.name;
          });
        },
      ),
    );
  }

  void _showCardTypeManagementDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue.shade600, Colors.blue.shade700],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.credit_card,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'اختيار نوع الكارت',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'اختر من الأنواع المتاحة أو أضف نوع جديد',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 28,
                        ),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Consumer<CardTypeProvider>(
                builder: (context, provider, _) {
                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Default card types section
                        _buildCardTypesSection(
                          'الأنواع الأساسية',
                          Icons.star,
                          Colors.orange,
                          _buildDefaultCardTypes(),
                        ),

                        const SizedBox(height: 24),

                        // Custom card types section
                        _buildCardTypesSection(
                          'الأنواع المخصصة',
                          Icons.edit,
                          Colors.purple,
                          _buildCustomCardTypes(provider),
                        ),

                        const SizedBox(height: 24),

                        // Add new card type section
                        _buildAddNewCardTypeSection(provider),

                        const SizedBox(height: 20),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم أنواع الكروت
  Widget _buildCardTypesSection(
    String title,
    IconData icon,
    Color color,
    Widget content,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        content,
      ],
    );
  }

  // بناء الأنواع الأساسية
  Widget _buildDefaultCardTypes() {
    final defaultTypes = [
      {
        'id': CardType.cash.name,
        'name': CardType.cash.displayName,
        'icon': Icons.money,
        'color': Colors.green,
      },
      {
        'id': CardType.zain.name,
        'name': CardType.zain.displayName,
        'icon': Icons.credit_card,
        'color': Colors.purple,
      },
      {
        'id': CardType.sia.name,
        'name': CardType.sia.displayName,
        'icon': Icons.credit_card,
        'color': Colors.red,
      },
      {
        'id': CardType.abuAshara.name,
        'name': CardType.abuAshara.displayName,
        'icon': Icons.credit_card,
        'color': Colors.teal,
      },
      {
        'id': CardType.abuSitta.name,
        'name': CardType.abuSitta.displayName,
        'icon': Icons.credit_card,
        'color': Colors.blue,
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: defaultTypes.length,
      itemBuilder: (context, index) {
        final type = defaultTypes[index];
        final isSelected = _selectedCardType == type['id'];

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedCardType = type['id'] as String;
              _cardTypeController.text = type['name'] as String;
            });
            Navigator.pop(context);
          },
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isSelected
                    ? [
                        (type['color'] as Color).withValues(alpha: 0.2),
                        (type['color'] as Color).withValues(alpha: 0.3),
                      ]
                    : [Colors.grey.shade50, Colors.grey.shade100],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? (type['color'] as Color)
                    : Colors.grey.shade300,
                width: isSelected ? 2 : 1,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: (type['color'] as Color).withValues(alpha: 0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ]
                  : null,
            ),
            child: Row(
              children: [
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: (type['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    type['icon'] as IconData,
                    color: type['color'] as Color,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    type['name'] as String,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: isSelected
                          ? FontWeight.bold
                          : FontWeight.w600,
                      color: isSelected
                          ? (type['color'] as Color)
                          : Colors.black87,
                    ),
                  ),
                ),
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: type['color'] as Color,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء الأنواع المخصصة
  Widget _buildCustomCardTypes(CardTypeProvider provider) {
    if (provider.customCardTypes.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          children: [
            Icon(Icons.inbox, color: Colors.grey.shade400, size: 48),
            const SizedBox(height: 12),
            Text(
              'لا توجد أنواع مخصصة',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'يمكنك إضافة أنواع كروت جديدة من الأسفل',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: provider.customCardTypes.length,
      itemBuilder: (context, index) {
        final cardType = provider.customCardTypes[index];
        final isSelected = _selectedCardType == 'custom_${cardType.id}';

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isSelected
                  ? [
                      Colors.purple.withValues(alpha: 0.1),
                      Colors.purple.withValues(alpha: 0.2),
                    ]
                  : [Colors.white, Colors.grey.shade50],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected ? Colors.purple : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Colors.purple.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ]
                : null,
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.credit_card,
                color: Colors.purple,
                size: 20,
              ),
            ),
            title: Text(
              cardType.displayName,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w600,
                color: isSelected ? Colors.purple : Colors.black87,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (!isSelected)
                  IconButton(
                    onPressed: () {
                      setState(() {
                        _selectedCardType = 'custom_${cardType.id}';
                        _cardTypeController.text = cardType.displayName;
                      });
                      Navigator.pop(context);
                    },
                    icon: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.green,
                        size: 18,
                      ),
                    ),
                    tooltip: 'اختيار',
                  ),
                IconButton(
                  onPressed: () =>
                      _deleteCardTypeInDialog(cardType.id!, provider),
                  icon: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.delete,
                      color: Colors.red,
                      size: 18,
                    ),
                  ),
                  tooltip: 'حذف',
                ),
                if (isSelected)
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء قسم إضافة نوع جديد
  Widget _buildAddNewCardTypeSection(CardTypeProvider provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.green.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.add_card,
                  color: Colors.green.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إضافة نوع جديد',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'أضف نوع كارت مخصص جديد',
                      style: TextStyle(fontSize: 14, color: Colors.green),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _newCardTypeController,
            decoration: InputDecoration(
              hintText: 'اسم نوع الكارت الجديد',
              prefixIcon: const Icon(Icons.credit_card),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _addNewCardType(provider),
              icon: const Icon(Icons.add, size: 20),
              label: const Text(
                'إضافة نوع الكارت',
                style: TextStyle(fontSize: 16),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _addNewCardType(CardTypeProvider provider) async {
    final name = _newCardTypeController.text.trim();
    if (name.isNotEmpty) {
      try {
        await provider.addCustomCardType(name);
        _newCardTypeController.clear();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إضافة نوع الكارت بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في إضافة نوع الكارت: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _deleteCardTypeInDialog(
    int cardTypeId,
    CardTypeProvider provider,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف نوع الكارت هذا؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await provider.deleteCustomCardType(cardTypeId);

        if (_selectedCardType == 'custom_$cardTypeId') {
          setState(() {
            _selectedCardType = null;
            _cardTypeController.clear();
          });
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف نوع الكارت بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف نوع الكارت: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _showQuantityDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange.shade600, Colors.orange.shade700],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.numbers,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إدارة الكمية',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'حدد كمية البطاقات المطلوبة',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 28,
                        ),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Consumer<FormDataProvider>(
                builder: (context, formDataProvider, _) {
                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Input section
                        _buildQuantityInputSection(),

                        const SizedBox(height: 24),

                        // Quick values section
                        _buildQuickValuesSection(
                          'القيم السريعة',
                          Icons.flash_on,
                          Colors.orange,
                          formDataProvider.quickQuantities,
                          (quantity) {
                            setState(() {
                              _quantityController.text = quantity.toString();
                            });
                          },
                          (quantity) async {
                            await formDataProvider.removeQuickQuantity(
                              quantity,
                            );
                            if (mounted && context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم حذف القيمة السريعة'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          },
                        ),

                        const SizedBox(height: 24),

                        // Add new quick value section
                        _buildAddQuickValueSection(
                          'إضافة قيمة سريعة جديدة',
                          _newQuantityController,
                          'كمية جديدة',
                          () async {
                            final value = int.tryParse(
                              _newQuantityController.text,
                            );
                            if (value != null && value > 0) {
                              await formDataProvider.addQuickQuantity(value);
                              _newQuantityController.clear();
                              if (mounted && context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('تم إضافة القيمة السريعة'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              }
                            }
                          },
                        ),

                        const SizedBox(height: 24),

                        // Confirm button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(Icons.check, size: 20),
                            label: const Text(
                              'تأكيد الكمية',
                              style: TextStyle(fontSize: 16),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم إدخال الكمية
  Widget _buildQuantityInputSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade50, Colors.orange.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.numbers,
                  color: Colors.orange.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدخال الكمية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'أدخل عدد البطاقات المطلوبة',
                      style: TextStyle(fontSize: 14, color: Colors.orange),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _quantityController,
            keyboardType: TextInputType.number,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            decoration: InputDecoration(
              hintText: 'أدخل الكمية',
              prefixIcon: const Icon(Icons.numbers),
              suffixIcon: _quantityController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        setState(() {
                          _quantityController.clear();
                        });
                      },
                      icon: Icon(Icons.clear, color: Colors.red.shade400),
                      tooltip: 'مسح',
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            onChanged: (value) {
              setState(() {}); // لتحديث زر الحذف
            },
          ),
        ],
      ),
    );
  }

  // بناء قسم القيم السريعة
  Widget _buildQuickValuesSection(
    String title,
    IconData icon,
    Color color,
    List<dynamic> values,
    Function(dynamic) onSelect,
    Function(dynamic) onDelete,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        if (values.isEmpty)
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade200),
            ),
            child: Column(
              children: [
                Icon(Icons.inbox, color: Colors.grey.shade400, size: 48),
                const SizedBox(height: 12),
                Text(
                  'لا توجد قيم سريعة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'يمكنك إضافة قيم سريعة من الأسفل',
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
                ),
              ],
            ),
          )
        else
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: values.map((value) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color.withValues(alpha: 0.1),
                      color.withValues(alpha: 0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: color.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () => onSelect(value),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        child: Text(
                          value.toString(),
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: color,
                          ),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () => onDelete(value),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.red.withValues(alpha: 0.1),
                          borderRadius: const BorderRadius.only(
                            topRight: Radius.circular(12),
                            bottomRight: Radius.circular(12),
                          ),
                        ),
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.red.shade600,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
      ],
    );
  }

  // بناء قسم إضافة قيمة سريعة
  Widget _buildAddQuickValueSection(
    String title,
    TextEditingController controller,
    String hint,
    VoidCallback onAdd,
  ) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.green.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(Icons.add, color: Colors.green.shade700, size: 24),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: hint,
                    prefixIcon: const Icon(Icons.add),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 16,
                    ),
                  ),
                  style: const TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(width: 12),
              ElevatedButton.icon(
                onPressed: onAdd,
                icon: const Icon(Icons.add, size: 20),
                label: const Text('إضافة', style: TextStyle(fontSize: 16)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showAmountDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.green.shade600, Colors.green.shade700],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.attach_money,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إدارة المبلغ',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'حدد مبلغ البطاقة الواحدة',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 28,
                        ),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Consumer<FormDataProvider>(
                builder: (context, formDataProvider, _) {
                  return SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Input section
                        _buildAmountInputSection(),

                        const SizedBox(height: 24),

                        // Quick values section
                        _buildQuickValuesSection(
                          'القيم السريعة',
                          Icons.flash_on,
                          Colors.green,
                          formDataProvider.quickAmounts,
                          (amount) {
                            setState(() {
                              _amountController.text = amount.toString();
                            });
                          },
                          (amount) async {
                            await formDataProvider.removeQuickAmount(amount);
                            if (mounted && context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم حذف القيمة السريعة'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          },
                        ),

                        const SizedBox(height: 24),

                        // Add new quick value section
                        _buildAddQuickValueSection(
                          'إضافة قيمة سريعة جديدة',
                          _newAmountController,
                          'مبلغ جديد',
                          () async {
                            final value = double.tryParse(
                              _newAmountController.text,
                            );
                            if (value != null && value > 0) {
                              await formDataProvider.addQuickAmount(value);
                              _newAmountController.clear();
                              if (mounted && context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('تم إضافة القيمة السريعة'),
                                    backgroundColor: Colors.green,
                                  ),
                                );
                              }
                            }
                          },
                        ),

                        const SizedBox(height: 24),

                        // Confirm button
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(Icons.check, size: 20),
                            label: const Text(
                              'تأكيد المبلغ',
                              style: TextStyle(fontSize: 16),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                          ),
                        ),

                        const SizedBox(height: 20),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم إدخال المبلغ
  Widget _buildAmountInputSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.green.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.attach_money,
                  color: Colors.green.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدخال المبلغ',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'أدخل مبلغ البطاقة الواحدة',
                      style: TextStyle(fontSize: 14, color: Colors.green),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            style: const TextStyle(
              color: Colors.black87,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            decoration: InputDecoration(
              hintText: 'أدخل المبلغ',
              prefixIcon: const Icon(Icons.attach_money),
              suffixIcon: _amountController.text.isNotEmpty
                  ? IconButton(
                      onPressed: () {
                        setState(() {
                          _amountController.clear();
                        });
                      },
                      icon: Icon(Icons.clear, color: Colors.red.shade400),
                      tooltip: 'مسح',
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            onChanged: (value) {
              setState(() {}); // لتحديث زر الحذف
            },
          ),
        ],
      ),
    );
  }

  void _showNotesDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.purple.shade600, Colors.purple.shade700],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                ),
              ),
              child: Column(
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.sticky_note_2,
                          color: Colors.white,
                          size: 28,
                        ),
                      ),
                      const SizedBox(width: 16),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'إدارة الملاحظات',
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'أضف ملاحظات مهمة للدين',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 28,
                        ),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.white.withValues(alpha: 0.2),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Input section
                    _buildNotesInputSection(),

                    const SizedBox(height: 24),

                    // Quick notes section
                    _buildQuickNotesSection(),

                    const SizedBox(height: 24),

                    // Confirm button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.check, size: 20),
                        label: const Text(
                          'تأكيد الملاحظات',
                          style: TextStyle(fontSize: 16),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.purple,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                      ),
                    ),

                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء قسم إدخال الملاحظات
  Widget _buildNotesInputSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.purple.shade50, Colors.purple.shade100],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.purple.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.purple.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.sticky_note_2,
                  color: Colors.purple.shade700,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إدخال الملاحظات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'أضف ملاحظات مهمة حول الدين',
                      style: TextStyle(fontSize: 14, color: Colors.purple),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _notesController,
            maxLines: 4,
            style: const TextStyle(color: Colors.black87, fontSize: 16),
            decoration: InputDecoration(
              hintText: 'أدخل ملاحظاتك هنا...',
              prefixIcon: const Padding(
                padding: EdgeInsets.only(bottom: 60),
                child: Icon(Icons.sticky_note_2),
              ),
              suffixIcon: _notesController.text.isNotEmpty
                  ? Container(
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: IconButton(
                        onPressed: () {
                          setState(() {
                            _notesController.clear();
                          });
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('تم مسح الملاحظات'),
                              backgroundColor: Colors.green,
                              duration: Duration(seconds: 1),
                            ),
                          );
                        },
                        icon: Icon(
                          Icons.delete_outline,
                          color: Colors.red.shade600,
                        ),
                        tooltip: 'مسح الملاحظات',
                      ),
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            onChanged: (value) {
              setState(() {}); // لتحديث زر الحذف
            },
          ),
        ],
      ),
    );
  }

  // بناء قسم الملاحظات السريعة
  Widget _buildQuickNotesSection() {
    return Consumer<FormDataProvider>(
      builder: (context, formDataProvider, _) {
        final quickNotes = formDataProvider.quickNotes;

        return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.purple.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.flash_on, color: Colors.purple, size: 20),
            ),
            const SizedBox(width: 12),
            const Text(
              'الملاحظات السريعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // زر مسح جميع الملاحظات
        if (_notesController.text.isNotEmpty) ...[
          Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 16),
            child: ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  _notesController.clear();
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم مسح جميع الملاحظات'),
                    backgroundColor: Colors.green,
                    duration: Duration(seconds: 1),
                  ),
                );
              },
              icon: Icon(Icons.delete_sweep, color: Colors.red.shade600),
              label: Text(
                'مسح جميع الملاحظات',
                style: TextStyle(color: Colors.red.shade600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade50,
                elevation: 0,
                side: BorderSide(color: Colors.red.shade200),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],

        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: quickNotes.map((note) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.purple.withValues(alpha: 0.1),
                    Colors.purple.withValues(alpha: 0.2),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.purple.withValues(alpha: 0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // النص القابل للنقر
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        if (_notesController.text.isNotEmpty) {
                          _notesController.text += ', $note';
                        } else {
                          _notesController.text = note;
                        }
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      child: Text(
                        note,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.purple.shade700,
                        ),
                      ),
                    ),
                  ),
                  // زر الحذف
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                    ),
                    child: IconButton(
                      onPressed: () async {
                        // إظهار تأكيد الحذف
                        final bool? shouldDelete = await showDialog<bool>(
                          context: context,
                          builder: (context) => AlertDialog(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            title: Row(
                              children: [
                                Icon(
                                  Icons.delete_outline,
                                  color: Colors.red.shade600,
                                ),
                                const SizedBox(width: 8),
                                const Text('حذف الملاحظة'),
                              ],
                            ),
                            content: Text('هل تريد حذف الملاحظة "$note"؟'),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context, false),
                                child: const Text('إلغاء'),
                              ),
                              ElevatedButton(
                                onPressed: () => Navigator.pop(context, true),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  foregroundColor: Colors.white,
                                ),
                                child: const Text('حذف'),
                              ),
                            ],
                          ),
                        );

                        if (shouldDelete == true && mounted) {
                          // حذف الملاحظة من القائمة
                          final formDataProvider =
                              Provider.of<FormDataProvider>(
                                context,
                                listen: false,
                              );
                          await formDataProvider.removeQuickNote(note);

                          if (mounted && context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('تم حذف الملاحظة "$note"'),
                                backgroundColor: Colors.green,
                                duration: const Duration(seconds: 2),
                                action: SnackBarAction(
                                  label: 'تراجع',
                                  textColor: Colors.white,
                                  onPressed: () async {
                                    // إعادة إضافة الملاحظة
                                    await formDataProvider.addQuickNote(note);
                                  },
                                ),
                              ),
                            );
                          }
                        }
                      },
                      icon: Icon(
                        Icons.close,
                        color: Colors.red.shade600,
                        size: 16,
                      ),
                      tooltip: 'حذف الملاحظة',
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: const EdgeInsets.all(4),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
      },
    );
  }

  // Clear form method
  void clearForm() {
    setState(() {
      _selectedCustomer = null;
      _selectedCardType = null;
      _customerNameController.clear();
      _quantityController.clear();
      _amountController.clear();
      _notesController.clear();
      _entryDate = DateTime.now();
      _dueDate = DateTime.now().add(const Duration(days: 7));
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم مسح النموذج'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  // Reset quick values method
  void resetQuickValues() async {
    final formDataProvider = Provider.of<FormDataProvider>(
      context,
      listen: false,
    );
    await formDataProvider.resetQuickValuesToDefaults();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إعادة تعيين القيم السريعة'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  // Build section header
  Widget buildSectionHeader(String title, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade700,
        ),
      ),
    );
  }

  // Build enhanced clickable field
  Widget buildEnhancedClickableField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required VoidCallback onTap,
    String? Function(String?)? validator,
    required Color color,
    required bool hasValue,
  }) {
    // تحديد لون الأيقونة حسب نوع الحقل
    Color iconColor;
    if (icon == Icons.person_outline) {
      iconColor = Colors.blue;
    } else if (icon == Icons.credit_card_outlined) {
      iconColor = Colors.purple;
    } else if (icon == Icons.numbers_outlined) {
      iconColor = Colors.green;
    } else if (icon == Icons.attach_money_outlined) {
      iconColor = Colors.orange;
    } else if (icon == Icons.sticky_note_2_outlined) {
      iconColor = Colors.indigo;
    } else {
      iconColor = Colors.grey.shade600;
    }

    return TextFormField(
      controller: controller,
      readOnly: true,
      onTap: onTap,
      validator: validator,
      style: TextStyle(
        color: hasValue ? Colors.black87 : Colors.grey.shade600,
        fontSize: 16,
        fontWeight: hasValue ? FontWeight.w500 : FontWeight.normal,
      ),
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: iconColor, size: 22),
        suffixIcon: hasValue
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.red.shade50,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: IconButton(
                      onPressed: () {
                        setState(() {
                          controller.clear();
                          // إعادة تعيين القيم المرتبطة
                          if (icon == Icons.person_outline) {
                            _selectedCustomer = null;
                          } else if (icon == Icons.credit_card_outlined) {
                            _selectedCardType = null;
                          } else if (icon == Icons.numbers_outlined) {
                            // مسح الكمية
                          } else if (icon == Icons.attach_money_outlined) {
                            // مسح المبلغ
                          } else if (icon == Icons.sticky_note_2_outlined) {
                            // مسح الملاحظات
                          }
                        });

                        // إظهار رسالة تأكيد
                        String message = 'تم المسح';
                        if (icon == Icons.person_outline) {
                          message = 'تم مسح العميل المحدد';
                        } else if (icon == Icons.credit_card_outlined) {
                          message = 'تم مسح نوع الكارت';
                        } else if (icon == Icons.numbers_outlined) {
                          message = 'تم مسح الكمية';
                        } else if (icon == Icons.attach_money_outlined) {
                          message = 'تم مسح المبلغ';
                        } else if (icon == Icons.sticky_note_2_outlined) {
                          message = 'تم مسح الملاحظات';
                        }

                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(message),
                            backgroundColor: Colors.green,
                            duration: const Duration(seconds: 1),
                          ),
                        );
                      },
                      icon: Icon(
                        Icons.delete_outline,
                        color: Colors.red.shade600,
                        size: 18,
                      ),
                      tooltip: 'مسح',
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey.shade400,
                    size: 16,
                  ),
                ],
              )
            : Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey.shade400,
                size: 16,
              ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.grey.shade500, width: 1.5),
        ),
        filled: true,
        fillColor: Colors.white,
        labelStyle: TextStyle(
          color: Colors.grey.shade600,
          fontWeight: FontWeight.normal,
        ),
      ),
    );
  }

  // Build enhanced date field
  Widget buildEnhancedDateField({
    required String label,
    required DateTime date,
    required VoidCallback onTap,
    required IconData icon,
    required Color color,
    bool showTime = false,
  }) {
    final dateFormat = DateFormat('yyyy/MM/dd', 'en');
    final timeFormat = DateFormat(
      'HH:mm',
      'en',
    ); // تغيير إلى 24 ساعة بدون AM/PM

    // أسماء الأيام بالعربية
    final List<String> arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    final dayName = arabicDays[date.weekday - 1];

    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.grey.shade50, // أبيض مائل للرمادي
          border: Border.all(color: Colors.black87), // حواف سوداء بسيطة
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label,
                    style: TextStyle(
                      fontSize: 14,
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    showTime
                        ? '$dayName - ${dateFormat.format(date)} ${timeFormat.format(date)}'
                        : '$dayName - ${dateFormat.format(date)}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.edit_calendar, color: color, size: 20),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - _animation.value) * 100),
          child: Opacity(
            opacity: _animation.value,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.95,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(25),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Enhanced Handle
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    width: 50,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),

                  // Enhanced Header with white background
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: () {
                              _saveFormData();
                              Navigator.pop(context);
                            },
                            icon: Icon(
                              Icons.close,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                'إضافة دين جديد',
                                style: Theme.of(context).textTheme.headlineSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'املأ البيانات المطلوبة لإضافة دين جديد',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 16),
                              // مؤشر التقدم
                              buildProgressIndicator(),
                            ],
                          ),
                        ),
                        // Quick Actions Menu
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: PopupMenuButton<String>(
                            icon: Icon(
                              Icons.more_vert,
                              color: Colors.grey.shade700,
                            ),
                            onSelected: (value) {
                              switch (value) {
                                case 'clear':
                                  clearForm();
                                  break;
                                case 'reset_quick':
                                  resetQuickValues();
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              const PopupMenuItem(
                                value: 'clear',
                                child: Row(
                                  children: [
                                    Icon(Icons.clear_all, size: 18),
                                    SizedBox(width: 8),
                                    Text('مسح النموذج'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'reset_quick',
                                child: Row(
                                  children: [
                                    Icon(Icons.refresh, size: 18),
                                    SizedBox(width: 8),
                                    Text('إعادة تعيين القيم السريعة'),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Enhanced Form with better spacing and design
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(24),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Customer Section with enhanced design
                            buildSectionHeader(
                              'بيانات العميل',
                              Icons.person,
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            buildEnhancedClickableField(
                              controller: _customerNameController,
                              label: 'العميل',
                              icon: Icons.person_outline,
                              onTap: () => _showCustomerSelectionDialog(),
                              validator: (value) {
                                if (_selectedCustomer == null) {
                                  return 'يرجى اختيار عميل';
                                }
                                return null;
                              },
                              color: Colors.blue,
                              hasValue: _selectedCustomer != null,
                            ),

                            const SizedBox(height: 16),

                            // Card Type Section
                            buildSectionHeader(
                              'نوع الكارت',
                              Icons.credit_card,
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            buildEnhancedClickableField(
                              controller: _cardTypeController,
                              label: 'نوع الكارت',
                              icon: Icons.credit_card_outlined,
                              onTap: () => _showCardTypeManagementDialog(),
                              validator: (value) {
                                if (_selectedCardType == null) {
                                  return 'يرجى اختيار نوع الكارت';
                                }
                                return null;
                              },
                              color: Colors.blue,
                              hasValue: _selectedCardType != null,
                            ),

                            const SizedBox(height: 16),

                            // Quantity Section
                            buildSectionHeader(
                              'الكمية',
                              Icons.numbers,
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            buildEnhancedClickableField(
                              controller: _quantityController,
                              label: 'الكمية',
                              icon: Icons.numbers_outlined,
                              onTap: () => _showQuantityDialog(),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'مطلوب';
                                }
                                if (int.tryParse(value) == null ||
                                    int.parse(value) <= 0) {
                                  return 'كمية غير صحيحة';
                                }
                                return null;
                              },
                              color: Colors.blue,
                              hasValue: _quantityController.text.isNotEmpty,
                            ),

                            const SizedBox(height: 16),

                            // Amount Section
                            buildSectionHeader(
                              'المبلغ',
                              Icons.attach_money,
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            buildEnhancedClickableField(
                              controller: _amountController,
                              label: 'المبلغ',
                              icon: Icons.attach_money_outlined,
                              onTap: () => _showAmountDialog(),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'مطلوب';
                                }
                                final cleanValue = value.replaceAll(',', '');
                                if (double.tryParse(cleanValue) == null ||
                                    double.parse(cleanValue) <= 0) {
                                  return 'مبلغ غير صحيح';
                                }
                                return null;
                              },
                              color: Colors.blue,
                              hasValue: _amountController.text.isNotEmpty,
                            ),

                            const SizedBox(height: 16),

                            // Notes Section
                            buildSectionHeader(
                              'ملاحظات',
                              Icons.sticky_note_2,
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),
                            buildEnhancedClickableField(
                              controller: _notesController,
                              label: 'ملاحظات (اختياري)',
                              icon: Icons.sticky_note_2_outlined,
                              onTap: () => _showNotesDialog(),
                              color: Colors.blue,
                              hasValue: _notesController.text.isNotEmpty,
                            ),

                            const SizedBox(height: 16),

                            // Dates Section
                            buildSectionHeader(
                              'التواريخ',
                              Icons.calendar_today,
                              Colors.blue,
                            ),
                            const SizedBox(height: 12),

                            // تاريخ القيد
                            buildEnhancedDateField(
                              label: 'تاريخ القيد',
                              date: _entryDate,
                              onTap: () => _selectDate(context, true),
                              icon: Icons.today_outlined,
                              showTime: true,
                              color: Colors.blue,
                            ),

                            const SizedBox(height: 12),

                            // تاريخ الاستحقاق
                            buildEnhancedDateField(
                              label: 'تاريخ الاستحقاق',
                              date: _dueDate,
                              onTap: () => _selectDate(context, false),
                              icon: Icons.event_available_outlined,
                              color: Colors.orange,
                            ),

                            const SizedBox(height: 24),

                            // معاينة سريعة للدين
                            if (isFormComplete()) buildDebtPreview(),

                            const SizedBox(height: 24),

                            // اختصارات سريعة
                            buildQuickActions(),

                            const SizedBox(height: 16),

                            // Enhanced Save Button
                            SizedBox(
                              width: double.infinity,
                              height: 60,
                              child: ElevatedButton(
                                onPressed: _isLoading
                                    ? null
                                    : (isFormComplete() ? _saveDebt : null),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: _isLoading
                                      ? Colors.grey.shade300
                                      : isFormComplete()
                                      ? Colors.blue.shade600
                                      : Colors.grey.shade200,
                                  foregroundColor: _isLoading
                                      ? Colors.grey.shade600
                                      : isFormComplete()
                                      ? Colors.white
                                      : Colors.grey.shade500,
                                  side: BorderSide(
                                    color: _isLoading
                                        ? Colors.grey.shade400
                                        : isFormComplete()
                                        ? Colors.blue.shade600
                                        : Colors.grey.shade300,
                                    width: 1.5,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  elevation: isFormComplete() ? 2 : 0,
                                ),
                                child: _isLoading
                                    ? Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            height: 20,
                                            width: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                    Colors.grey.shade600,
                                                  ),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Text(
                                            'جاري الحفظ...',
                                            style: TextStyle(
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.grey.shade600,
                                            ),
                                          ),
                                        ],
                                      )
                                    : Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            isFormComplete()
                                                ? Icons.save
                                                : Icons.edit_note,
                                            color: isFormComplete()
                                                ? Colors.white
                                                : Colors.grey.shade500,
                                            size: 24,
                                          ),
                                          const SizedBox(width: 12),
                                          Text(
                                            isFormComplete()
                                                ? 'إضافة الدين'
                                                : 'أكمل البيانات المطلوبة',
                                            style: TextStyle(
                                              fontSize: isFormComplete()
                                                  ? 18
                                                  : 16,
                                              fontWeight: FontWeight.bold,
                                              color: isFormComplete()
                                                  ? Colors.white
                                                  : Colors.grey.shade500,
                                            ),
                                          ),
                                        ],
                                      ),
                              ),
                            ),

                            const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // مؤشر التقدم
  Widget buildProgressIndicator() {
    int completedSteps = 0;
    const int totalSteps = 5; // العميل، نوع الكارت، الكمية، المبلغ، التواريخ

    if (_selectedCustomer != null) completedSteps++;
    if (_selectedCardType != null) completedSteps++;
    if (_quantityController.text.isNotEmpty) completedSteps++;
    if (_amountController.text.isNotEmpty) completedSteps++;
    // التواريخ دائماً موجودة (لها قيم افتراضية)
    completedSteps++;

    final progress = completedSteps / totalSteps;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: Colors.grey.shade200,
                valueColor: AlwaysStoppedAnimation<Color>(
                  progress == 1.0 ? Colors.green : Colors.blue,
                ),
                minHeight: 6,
              ),
            ),
            const SizedBox(width: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: (progress == 1.0 ? Colors.green : Colors.blue)
                    .withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$completedSteps/$totalSteps',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: progress == 1.0 ? Colors.green : Colors.blue,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          progress == 1.0
              ? '✅ جميع البيانات مكتملة - جاهز للحفظ!'
              : 'أكمل البيانات المطلوبة (${(progress * 100).toInt()}%)',
          style: TextStyle(
            fontSize: 11,
            color: progress == 1.0
                ? Colors.green.shade600
                : Colors.grey.shade600,
            fontWeight: progress == 1.0 ? FontWeight.w600 : FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // التحقق من اكتمال النموذج
  bool isFormComplete() {
    return _selectedCustomer != null &&
        _selectedCardType != null &&
        _quantityController.text.isNotEmpty &&
        _amountController.text.isNotEmpty;
  }

  // معاينة سريعة للدين
  Widget buildDebtPreview() {
    final quantity = int.tryParse(_quantityController.text) ?? 0;
    final amount =
        double.tryParse(_amountController.text.replaceAll(',', '')) ?? 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.blue.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.shade600,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(Icons.preview, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              const Text(
                'معاينة الدين',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          buildPreviewRow(
            'العميل',
            _selectedCustomer?.name ?? '',
            Icons.person,
          ),
          buildPreviewRow('نوع الكارت', getCardTypeName(), Icons.credit_card),
          buildPreviewRow('الكمية', '$quantity كارت', Icons.numbers),
          buildPreviewRow(
            'المبلغ',
            '${NumberFormat('#,###', 'en').format(amount)} د.ع',
            Icons.attach_money,
          ),
          if (_notesController.text.isNotEmpty)
            buildPreviewRow('الملاحظات', _notesController.text, Icons.note),
        ],
      ),
    );
  }

  Widget buildPreviewRow(
    String label,
    String value,
    IconData icon, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: isTotal ? Colors.green.shade600 : Colors.blue.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                fontSize: isTotal ? 14 : 13,
                fontWeight: isTotal ? FontWeight.bold : FontWeight.w600,
                color: isTotal ? Colors.green.shade700 : Colors.black87,
              ),
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }

  String getCardTypeName() {
    if (_selectedCardType == null) return '';

    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    if (_selectedCardType!.startsWith('custom_')) {
      final cardTypeId = int.parse(
        _selectedCardType!.replaceFirst('custom_', ''),
      );
      final customCardType = cardTypeProvider.customCardTypes.firstWhere(
        (ct) => ct.id == cardTypeId,
        orElse: () => throw Exception('Card type not found'),
      );
      return customCardType.displayName;
    }

    return _selectedCardType!;
  }

  // اختصارات سريعة
  Widget buildQuickActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flash_on, size: 16, color: Colors.orange.shade600),
              const SizedBox(width: 8),
              Text(
                'اختصارات سريعة',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              buildQuickActionChip(
                'تعبئة تلقائية',
                Icons.auto_fix_high,
                Colors.blue,
                () => autoFillForm(),
              ),
              buildQuickActionChip(
                'مسح الكل',
                Icons.clear_all,
                Colors.red,
                () => clearForm(),
              ),
              buildQuickActionChip(
                'نسخ آخر دين',
                Icons.copy,
                Colors.green,
                () => copyLastDebt(),
              ),
              buildQuickActionChip(
                'حفظ كمسودة',
                Icons.drafts,
                Colors.orange,
                () => saveAsDraft(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget buildQuickActionChip(
    String label,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 14, color: color),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void autoFillForm() {
    // تعبئة تلقائية بقيم افتراضية للاختبار
    setState(() {
      _quantityController.text = '1';
      _amountController.text = '7,500';
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم ملء النموذج بقيم افتراضية'),
        backgroundColor: Colors.blue,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void copyLastDebt() {
    // نسخ بيانات آخر دين (يمكن تطويرها لاحقاً)
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة نسخ آخر دين قيد التطوير'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void saveAsDraft() {
    // حفظ كمسودة (يمكن تطويرها لاحقاً)
    _saveFormData();
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم حفظ البيانات كمسودة'),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 2),
      ),
    );
  }
}

// نافذة اختيار العميل المحسنة
class _CustomerSelectionBottomSheet extends void void StatefulWidget {
  const _CustomerSelectionBottomSheet({required this.onCustomerSelected});

  final Function(Customer) onCustomerSelected;

  @override
  State<_CustomerSelectionBottomSheet> createState() =>
      _CustomerSelectionBottomSheetState();
}

class _CustomerSelectionBottomSheetState
    extends void void State<_CustomerSelectionBottomSheet>
    with void void SingleTickerProviderStateMixin {
  final TextEditingController searchController = TextEditingController();
  final ScrollController scrollController = ScrollController();
  late AnimationController animationController;
  late Animation<double> animation;

  List<Customer> filteredCustomers = [];
  List<Customer> allCustomers = [];
  String searchQuery = '';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();

    animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    animation = CurvedAnimation(
      parent: animationController,
      curve: Curves.easeInOut,
    );

    animationController.forward();

    // تأخير تحميل العملاء لتجنب استدعاء setState أثناء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      loadCustomers();
    });

    searchController.addListener(() {
      setState(() {
        searchQuery = searchController.text;
        filterCustomers();
      });
    });
  }

  @override
  void dispose() {
    animationController.dispose();
    searchController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  Future<void> loadCustomers() async {
    try {
      final customerProvider = Provider.of<CustomerProvider>(
        context,
        listen: false,
      );
      await customerProvider.loadCustomers();

      setState(() {
        allCustomers = customerProvider.customers;
        filteredCustomers = allCustomers;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  void filterCustomers() {
    if (searchQuery.isEmpty) {
      filteredCustomers = allCustomers;
    } else {
      final query = searchQuery.toLowerCase().trim();
      filteredCustomers = allCustomers.where((customer) {
        final name = customer.name.toLowerCase();
        final phone = customer.phone?.toLowerCase() ?? '';
        const address = '';

        // البحث المتقدم
        return name.contains(query) ||
            phone.contains(query) ||
            address.contains(query) ||
            isPhoneNumberMatch(phone, query) ||
            isNameMatch(name, query);
      }).toList();

      // ترتيب النتائج حسب الصلة
      filteredCustomers.sort((a, b) {
        final aName = a.name.toLowerCase();
        final bName = b.name.toLowerCase();

        // الأولوية للمطابقة الكاملة
        if (aName.startsWith(query) && !bName.startsWith(query)) return -1;
        if (!aName.startsWith(query) && bName.startsWith(query)) return 1;

        // ثم الترتيب الأبجدي
        return aName.compareTo(bName);
      });
    }
  }

  bool isPhoneNumberMatch(String phone, String query) {
    // إزالة الرموز والمسافات من رقم الهاتف
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    final cleanQuery = query.replaceAll(RegExp(r'[^\d]'), '');

    if (cleanQuery.isEmpty) return false;

    return cleanPhone.contains(cleanQuery);
  }

  bool isNameMatch(String name, String query) {
    // البحث في كلمات منفصلة
    final nameWords = name.split(' ');
    final queryWords = query.split(' ');

    for (final queryWord in queryWords) {
      if (queryWord.trim().isEmpty) continue;

      bool found = false;
      for (final nameWord in nameWords) {
        if (nameWord.toLowerCase().contains(queryWord.toLowerCase())) {
          found = true;
          break;
        }
      }
      if (!found) return false;
    }

    return queryWords.isNotEmpty;
  }

  void showAddCustomerDialog() {
    final nameController = TextEditingController();
    final phoneController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.person_add,
                color: Colors.green.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text('إضافة عميل جديد'),
          ],
        ),
        content: ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 400),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                style: const TextStyle(color: Colors.black87, fontSize: 16),
                decoration: InputDecoration(
                  labelText: 'اسم العميل *',
                  prefixIcon: const Icon(Icons.person, color: Colors.blue),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: phoneController,
                keyboardType: TextInputType.phone,
                maxLength: 11,
                style: const TextStyle(color: Colors.black87, fontSize: 16),
                decoration: InputDecoration(
                  labelText: 'رقم الهاتف (اختياري)',
                  hintText: '07xxxxxxxxx',
                  prefixIcon: const Icon(Icons.phone, color: Colors.green),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                  fillColor: Colors.grey.shade50,
                  counterText: '',
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.trim().isNotEmpty) {
                // التحقق من صحة رقم الهاتف
                final phone = phoneController.text.trim();
                if (phone.isNotEmpty && phone.length < 11) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('رقم الهاتف يجب أن يكون 11 رقم'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                try {
                  final customerProvider = Provider.of<CustomerProvider>(
                    context,
                    listen: false,
                  );
                  final newCustomer = Customer(
                    name: nameController.text.trim(),
                    phone: phone.isEmpty ? null : phone,
                    createdAt: DateTime.now(),
                    updatedAt: DateTime.now(),
                  );

                  await customerProvider.addCustomer(newCustomer);

                  // تحديث القائمة
                  await loadCustomers();

                  if (mounted && context.mounted) {
                    Navigator.pop(context);

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('تم إضافة العميل بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted && context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('خطأ في إضافة العميل: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  Widget buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.person_search, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            searchQuery.isEmpty ? 'لا توجد عملاء' : 'لم يتم العثور على عملاء',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            searchQuery.isEmpty
                ? 'أضف عميل جديد للبدء'
                : 'جرب البحث بكلمات مختلفة',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget buildCustomerItem(Customer customer, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue.shade400, Colors.blue.shade600],
            ),
            borderRadius: BorderRadius.circular(25),
          ),
          child: Center(
            child: Text(
              customer.name.isNotEmpty ? customer.name[0].toUpperCase() : '؟',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        title: Text(
          customer.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        subtitle: customer.phone != null
            ? Row(
                children: [
                  Icon(Icons.phone, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    customer.phone!,
                    style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                  ),
                ],
              )
            : null,
        trailing: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.green.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.green.shade200),
          ),
          child: Icon(Icons.check, color: Colors.green.shade600, size: 20),
        ),
        onTap: () {
          widget.onCustomerSelected(customer);
          Navigator.pop(context);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, (1 - animation.value) * 100),
          child: Opacity(
            opacity: animation.value,
            child: Container(
              height: MediaQuery.of(context).size.height * 0.9,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
              ),
              child: Column(
                children: [
                  // Handle
                  Container(
                    margin: const EdgeInsets.only(top: 12),
                    width: 50,
                    height: 5,
                    decoration: BoxDecoration(
                      color: Colors.grey.shade300,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),

                  // Header
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: Icon(
                              Icons.close,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                'اختيار العميل',
                                style: Theme.of(context).textTheme.headlineSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'اختر عميل من القائمة أو أضف عميل جديد',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                  fontSize: 12,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                        // أزرار الإجراءات
                        Row(
                          children: [
                            // زر إضافة عميل جديد
                            Container(
                              decoration: BoxDecoration(
                                color: Colors.green.shade50,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.green.shade200,
                                ),
                              ),
                              child: IconButton(
                                onPressed: showAddCustomerDialog,
                                icon: Icon(
                                  Icons.person_add,
                                  color: Colors.green.shade700,
                                ),
                                tooltip: 'إضافة عميل جديد',
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Enhanced Search Bar
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        TextField(
                          controller: searchController,
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 16,
                          ),
                          decoration: InputDecoration(
                            hintText:
                                'البحث عن عميل بالاسم، رقم الهاتف، أو العنوان...',
                            prefixIcon: Container(
                              margin: const EdgeInsets.all(8),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade50,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.search,
                                color: Colors.blue.shade600,
                                size: 20,
                              ),
                            ),
                            suffixIcon: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (searchQuery.isNotEmpty) ...[
                                  Container(
                                    margin: const EdgeInsets.only(right: 4),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 4,
                                    ),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade100,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      '${filteredCustomers.length}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.blue.shade700,
                                      ),
                                    ),
                                  ),
                                  IconButton(
                                    onPressed: () {
                                      searchController.clear();
                                    },
                                    icon: Icon(
                                      Icons.clear,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ] else ...[
                                  IconButton(
                                    onPressed: () {
                                      // إظهار نصائح البحث
                                      showSearchTips();
                                    },
                                    icon: Icon(
                                      Icons.help_outline,
                                      color: Colors.grey.shade600,
                                    ),
                                    tooltip: 'نصائح البحث',
                                  ),
                                ],
                              ],
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: Colors.blue.shade400,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 12,
                            ),
                          ),
                        ),

                        // شريط الفلاتر السريعة
                        if (searchQuery.isNotEmpty &&
                            filteredCustomers.isNotEmpty)
                          Container(
                            margin: const EdgeInsets.only(top: 8),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.filter_list,
                                  size: 16,
                                  color: Colors.grey.shade600,
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Row(
                                      children: [
                                        buildFilterChip(
                                          'الكل',
                                          filteredCustomers.length,
                                          true,
                                        ),
                                        const SizedBox(width: 8),
                                        buildFilterChip(
                                          'لديه هاتف',
                                          filteredCustomers
                                              .where((c) => c.phone != null)
                                              .length,
                                          false,
                                        ),
                                        const SizedBox(width: 8),
                                        buildFilterChip(
                                          'لديه عنوان',
                                          0,
                                          false,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ),

                  // Content
                  Expanded(
                    child: isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : filteredCustomers.isEmpty
                        ? buildEmptyState()
                        : ListView.builder(
                            controller: scrollController,
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            itemCount: filteredCustomers.length,
                            itemBuilder: (context, index) {
                              final customer = filteredCustomers[index];
                              return buildCustomerItem(customer, index);
                            },
                          ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void showSearchTips() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.lightbulb,
                color: Colors.blue.shade600,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text('نصائح البحث'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '💡 يمكنك البحث بـ:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('• الاسم الكامل أو جزء منه'),
            Text('• رقم الهاتف أو جزء منه'),
            Text('• كلمات منفصلة من الاسم'),
            SizedBox(height: 12),
            Text('🔍 أمثلة:', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text('• "أحمد" - للبحث عن جميع الأحمد'),
            Text('• "077" - للبحث برقم الهاتف'),
            Text('• "أحمد علي" - للبحث بالاسم الكامل'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('فهمت'),
          ),
        ],
      ),
    );
  }

  Widget buildFilterChip(String label, int count, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isSelected ? Colors.blue.shade100 : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected ? Colors.blue.shade300 : Colors.grey.shade300,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              color: isSelected ? Colors.blue.shade700 : Colors.grey.shade700,
            ),
          ),
          const SizedBox(width: 4),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: isSelected ? Colors.blue.shade200 : Colors.grey.shade200,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              count.toString(),
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: isSelected ? Colors.blue.shade800 : Colors.grey.shade600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
