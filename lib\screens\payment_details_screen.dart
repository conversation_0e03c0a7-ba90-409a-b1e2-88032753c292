import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/payment.dart';
import '../providers/card_type_provider.dart';

class PaymentDetailsScreen extends StatelessWidget {
  const PaymentDetailsScreen({
    super.key,
    required this.payment,
    required this.debt,
    required this.customer,
  });

  final Payment payment;
  final Debt? debt;
  final Customer customer;

  @override
  Widget build(BuildContext context) {
    final arabicFormatter = NumberFormat('#,###', 'en');
    final dateFormatter = DateFormat('yyyy/MM/dd', 'en');
    final timeFormatter = DateFormat('h:mm a', 'en');

    // Calculate statistics
    final daysToPayment = payment.paymentDate
        .difference(debt?.entryDate ?? DateTime.now())
        .inDays;
    final daysDifference = payment.paymentDate
        .difference(debt?.dueDate ?? DateTime.now())
        .inDays;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'تفاصيل التسديد',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back),
        ),
        actions: [
          IconButton(
            onPressed: () => _sharePaymentDetails(context),
            icon: const Icon(Icons.share),
          ),
          IconButton(
            onPressed: () => _printPaymentDetails(context),
            icon: const Icon(Icons.print),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card - Payment Summary
            _buildHeaderCard(context, arabicFormatter),

            const SizedBox(height: 16),

            // Payment Information Card
            _buildPaymentInfoCard(
              context,
              arabicFormatter,
              dateFormatter,
              timeFormatter,
            ),

            const SizedBox(height: 16),

            // Customer & Debt Information Card
            if (debt != null)
              _buildDebtInfoCard(context, arabicFormatter, dateFormatter),

            const SizedBox(height: 16),

            // Time Analytics Card
            if (debt != null)
              _buildTimeAnalyticsCard(context, daysToPayment, daysDifference),

            const SizedBox(height: 16),

            // Payment Status Card
            if (debt != null) _buildPaymentStatusCard(context, daysDifference),

            const SizedBox(height: 16),

            // Notes Card
            if (payment.notes != null && payment.notes!.isNotEmpty)
              _buildNotesCard(context),

            const SizedBox(height: 16),

            // Action Buttons
            _buildActionButtons(context),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCard(BuildContext context, NumberFormat arabicFormatter) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade600, Colors.blue.shade400],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            payment.type == PaymentType.full
                ? Icons.check_circle
                : Icons.pie_chart,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          Text(
            payment.type == PaymentType.full ? 'تسديد كامل' : 'تسديد جزئي',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${arabicFormatter.format(payment.amount.toInt())} د.ع',
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.w900,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'للعميل: ${customer.name}',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfoCard(
    BuildContext context,
    NumberFormat arabicFormatter,
    DateFormat dateFormatter,
    DateFormat timeFormatter,
  ) {
    return _buildCard(
      title: 'معلومات التسديد',
      icon: Icons.payment,
      color: Colors.green,
      child: Column(
        children: [
          _buildInfoRow(
            icon: Icons.calendar_today,
            label: 'تاريخ التسديد',
            value:
                '${_getArabicDayName(payment.paymentDate)} ${dateFormatter.format(payment.paymentDate)}',
            color: Colors.green,
          ),
          _buildInfoRow(
            icon: Icons.access_time,
            label: 'وقت التسديد',
            value: timeFormatter.format(payment.paymentDate),
            color: Colors.blue,
          ),
          _buildInfoRow(
            icon: Icons.account_balance_wallet,
            label: 'المبلغ المسدد',
            value: '${arabicFormatter.format(payment.amount.toInt())} د.ع',
            color: Colors.orange,
          ),
          _buildInfoRow(
            icon: Icons.category,
            label: 'نوع التسديد',
            value: payment.type == PaymentType.full
                ? 'تسديد كامل'
                : 'تسديد جزئي',
            color: Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildDebtInfoCard(
    BuildContext context,
    NumberFormat arabicFormatter,
    DateFormat dateFormatter,
  ) {
    return _buildCard(
      title: 'معلومات الدين',
      icon: Icons.receipt_long,
      color: Colors.orange,
      child: Column(
        children: [
          _buildInfoRow(
            icon: Icons.person,
            label: 'اسم العميل',
            value: customer.name,
            color: Colors.blue,
          ),
          _buildInfoRow(
            icon: Icons.schedule,
            label: 'تاريخ الاستحقاق',
            value:
                '${_getArabicDayName(debt!.dueDate)} ${dateFormatter.format(debt!.dueDate)}',
            color: Colors.red,
          ),
          _buildInfoRow(
            icon: Icons.calendar_month,
            label: 'تاريخ الإدخال',
            value:
                '${_getArabicDayName(debt!.entryDate)} ${dateFormatter.format(debt!.entryDate)}',
            color: Colors.green,
          ),
          Consumer<CardTypeProvider>(
            builder: (context, cardTypeProvider, _) {
              final cardTypeOption = cardTypeProvider.getCardTypeById(
                debt!.cardType,
              );
              final displayName =
                  cardTypeOption?.displayName ?? 'نوع غير معروف';
              return _buildInfoRow(
                icon: Icons.credit_card,
                label: 'نوع الكارت',
                value: displayName,
                color: Colors.purple,
              );
            },
          ),
          _buildInfoRow(
            icon: Icons.money,
            label: 'إجمالي المبلغ',
            value: '${arabicFormatter.format(debt!.amount.toInt())} د.ع',
            color: Colors.teal,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeAnalyticsCard(
    BuildContext context,
    int daysToPayment,
    int daysDifference,
  ) {
    return _buildCard(
      title: 'تحليل الأوقات',
      icon: Icons.analytics,
      color: Colors.indigo,
      child: Column(
        children: [
          _buildInfoRow(
            icon: Icons.timer,
            label: 'مدة التسديد',
            value: '$daysToPayment ${daysToPayment == 1 ? 'يوم' : 'يوم'}',
            color: Colors.blue,
          ),
          _buildInfoRow(
            icon: Icons.schedule_outlined,
            label: 'منذ التسديد',
            value: _formatTimeSincePayment(payment.paymentDate),
            color: Colors.green,
          ),
          _buildInfoRow(
            icon: Icons.trending_up,
            label: 'حالة التسديد',
            value: _getPaymentStatusText(daysDifference),
            color: _getStatusColor(daysDifference),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentStatusCard(BuildContext context, int daysDifference) {
    final isEarly = daysDifference < 0;
    final isOnTime = daysDifference == 0;

    final Color statusColor = isEarly
        ? Colors.green
        : isOnTime
        ? Colors.blue
        : Colors.red;
    final IconData statusIcon = isEarly
        ? Icons.fast_forward
        : isOnTime
        ? Icons.check_circle
        : Icons.warning;
    final String statusText = isEarly
        ? 'تسديد مبكر'
        : isOnTime
        ? 'تسديد في الوقت'
        : 'تسديد متأخر';

    return _buildCard(
      title: 'حالة التسديد',
      icon: statusIcon,
      color: statusColor,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: statusColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: statusColor.withValues(alpha: 0.3)),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor,
                shape: BoxShape.circle,
              ),
              child: Icon(statusIcon, color: Colors.white, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    statusText,
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _getPaymentStatusText(daysDifference),
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(BuildContext context) {
    return _buildCard(
      title: 'ملاحظات إضافية',
      icon: Icons.note_alt,
      color: Colors.amber,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.amber.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
        ),
        child: Text(
          payment.notes!,
          style: const TextStyle(fontSize: 16, height: 1.5),
        ),
      ),
    );
  }

  Widget _buildCard({
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: Colors.white, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
          // Card Content
          Padding(padding: const EdgeInsets.all(16), child: child),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          // Value (Left side)
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.start,
            ),
          ),

          // Label (Right side)
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.end,
            ),
          ),

          const SizedBox(width: 16),

          // Icon (Far right)
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, size: 18, color: color),
          ),
        ],
      ),
    );
  }

  String _getArabicDayName(DateTime date) {
    const arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return arabicDays[date.weekday - 1];
  }

  String _getPaymentStatusText(int daysDifference) {
    if (daysDifference < 0) {
      final daysEarly = daysDifference.abs();
      if (daysEarly == 1) {
        return 'مسدد قبل الموعد بيوم واحد';
      } else {
        return 'مسدد قبل الموعد بـ $daysEarly يوم';
      }
    } else if (daysDifference == 0) {
      return 'مسدد في الموعد المحدد';
    } else {
      if (daysDifference == 1) {
        return 'مسدد متأخر بيوم واحد';
      } else {
        return 'مسدد متأخر بـ $daysDifference يوم';
      }
    }
  }

  Color _getStatusColor(int daysDifference) {
    if (daysDifference < 0) {
      return Colors.green; // Early
    } else if (daysDifference == 0) {
      return Colors.blue; // On time
    } else {
      return Colors.red; // Late
    }
  }

  String _formatTimeSincePayment(DateTime paymentDate) {
    final now = DateTime.now();
    final difference = now.difference(paymentDate);

    if (difference.inDays > 0) {
      if (difference.inDays == 1) {
        return 'منذ يوم واحد';
      } else {
        return 'منذ ${difference.inDays} يوم';
      }
    } else if (difference.inHours > 0) {
      if (difference.inHours == 1) {
        return 'منذ ساعة واحدة';
      } else {
        return 'منذ ${difference.inHours} ساعات';
      }
    } else if (difference.inMinutes > 0) {
      if (difference.inMinutes == 1) {
        return 'منذ دقيقة واحدة';
      } else {
        return 'منذ ${difference.inMinutes} دقائق';
      }
    } else {
      return 'الآن';
    }
  }

  void _sharePaymentDetails(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المشاركة قيد التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _printPaymentDetails(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة الطباعة قيد التطوير'),
        backgroundColor: Colors.green,
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _sharePaymentDetails(context),
            icon: const Icon(Icons.share, size: 18),
            label: const Text('مشاركة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _printPaymentDetails(context),
            icon: const Icon(Icons.print, size: 18),
            label: const Text('طباعة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close, size: 18),
            label: const Text('إغلاق'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey[700],
              side: BorderSide(color: Colors.grey.shade300),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
