class CardStock {
  final int? id;
  final String cardTypeId;
  final int quantity;
  final int minQuantity;
  final DateTime createdAt;
  final DateTime updatedAt;

  CardStock({
    this.id,
    required this.cardTypeId,
    required this.quantity,
    required this.minQuantity,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'card_type_id': cardTypeId,
      'quantity': quantity,
      'min_quantity': minQuantity,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  factory CardStock.fromMap(Map<String, dynamic> map) {
    return CardStock(
      id: map['id']?.toInt(),
      cardTypeId: map['card_type_id'] ?? '',
      quantity: map['quantity']?.toInt() ?? 0,
      minQuantity: map['min_quantity']?.toInt() ?? 10,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  CardStock copyWith({
    int? id,
    String? cardTypeId,
    int? quantity,
    int? minQuantity,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CardStock(
      id: id ?? this.id,
      cardTypeId: cardTypeId ?? this.cardTypeId,
      quantity: quantity ?? this.quantity,
      minQuantity: minQuantity ?? this.minQuantity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CardStock(id: $id, cardTypeId: $cardTypeId, quantity: $quantity, minQuantity: $minQuantity)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CardStock &&
        other.id == id &&
        other.cardTypeId == cardTypeId &&
        other.quantity == quantity &&
        other.minQuantity == minQuantity;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        cardTypeId.hashCode ^
        quantity.hashCode ^
        minQuantity.hashCode;
  }
}
