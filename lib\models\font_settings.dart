class FontSettings {
  // Constructor
  FontSettings({
    this.fontSize = 1.0, // نسبة التكبير (1.0 = حجم عادي)
    this.fontFamily = 'Cairo', // نوع الخط الافتراضي
    this.fontWeight = 'normal', // وزن الخط
    this.lineHeight = 1.2, // ارتفاع السطر
  });

  factory FontSettings.fromJson(Map<String, dynamic> json) {
    return FontSettings(
      fontSize: (json['fontSize'] ?? 1.0).toDouble(),
      fontFamily: json['fontFamily'] ?? 'Cairo',
      fontWeight: json['fontWeight'] ?? 'normal',
      lineHeight: (json['lineHeight'] ?? 1.2).toDouble(),
    );
  }

  // Fields
  final double fontSize; // نسبة التكبير من 0.8 إلى 2.0
  final String fontFamily; // نوع الخط
  final String fontWeight; // وزن الخط: normal, bold
  final double lineHeight; // ارتفاع السطر

  // قائمة الخطوط المتاحة
  static const List<String> availableFonts = [
    'Cairo',
    'Amiri',
    'Tajawal',
    'Almarai',
    'Changa',
    'Lateef',
    'Scheherazade',
    'Noto Sans Arabic',
    'IBM Plex Sans Arabic',
    'Markazi Text',
  ];

  // قائمة أحجام الخط المتاحة
  static const Map<String, double> fontSizes = {
    'صغير جداً': 0.8,
    'صغير': 0.9,
    'عادي': 1.0,
    'كبير': 1.1,
    'كبير جداً': 1.2,
    'ضخم': 1.4,
    'ضخم جداً': 1.6,
    'عملاق': 1.8,
    'عملاق جداً': 2.0,
  };

  // قائمة أوزان الخط
  static const Map<String, String> fontWeights = {
    'عادي': 'normal',
    'عريض': 'bold',
  };

  Map<String, dynamic> toJson() {
    return {
      'fontSize': fontSize,
      'fontFamily': fontFamily,
      'fontWeight': fontWeight,
      'lineHeight': lineHeight,
    };
  }

  FontSettings copyWith({
    double? fontSize,
    String? fontFamily,
    String? fontWeight,
    double? lineHeight,
  }) {
    return FontSettings(
      fontSize: fontSize ?? this.fontSize,
      fontFamily: fontFamily ?? this.fontFamily,
      fontWeight: fontWeight ?? this.fontWeight,
      lineHeight: lineHeight ?? this.lineHeight,
    );
  }

  // الحصول على اسم حجم الخط
  String get fontSizeName {
    for (final entry in fontSizes.entries) {
      if (entry.value == fontSize) {
        return entry.key;
      }
    }
    return 'مخصص';
  }

  // الحصول على اسم وزن الخط
  String get fontWeightName {
    for (final entry in fontWeights.entries) {
      if (entry.value == fontWeight) {
        return entry.key;
      }
    }
    return 'عادي';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FontSettings &&
        other.fontSize == fontSize &&
        other.fontFamily == fontFamily &&
        other.fontWeight == fontWeight &&
        other.lineHeight == lineHeight;
  }

  @override
  int get hashCode {
    return fontSize.hashCode ^
        fontFamily.hashCode ^
        fontWeight.hashCode ^
        lineHeight.hashCode;
  }
}
