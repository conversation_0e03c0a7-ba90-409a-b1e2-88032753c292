import 'package:flutter/material.dart';

// Custom Date Picker with larger size for Honor screens
Future<DateTime?> showCustomDatePicker({
  required BuildContext context,
  required DateTime initialDate,
  required DateTime firstDate,
  required DateTime lastDate,
  Color? primaryColor,
}) async {
  return showDatePicker(
    context: context,
    initialDate: initialDate,
    firstDate: firstDate,
    lastDate: lastDate,
    locale: const Locale('ar', 'SA'),
    builder: (context, child) {
      return Theme(
        data: Theme.of(context).copyWith(
          colorScheme: ColorScheme.light(
            primary: primaryColor ?? Colors.blue.shade600,
            onSurface: Colors.black87,
          ),
          textButtonTheme: TextButtonThemeData(
            style: TextButton.styleFrom(
              foregroundColor: primaryColor ?? Colors.blue.shade600,
            ),
          ),
        ),
        child: Transform.scale(
          scale: 1.2, // تكبير النافذة بنسبة 20%
          child: SizedBox(
            width: MediaQuery.of(context).size.width * 0.95, // عرض أكبر
            height: MediaQuery.of(context).size.height * 0.7, // ارتفاع أكبر
            child: child!,
          ),
        ),
      );
    },
  );
}
