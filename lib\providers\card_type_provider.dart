import 'package:flutter/foundation.dart';
import '../models/custom_card_type.dart';
import '../database/database_helper.dart';

class CardTypeProvider with ChangeNotifier {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<CustomCardType> _customCardTypes = [];
  bool _isLoading = false;

  List<CustomCardType> get customCardTypes => _customCardTypes;
  bool get isLoading => _isLoading;

  // الحصول على جميع أنواع الكروت (المخصصة فقط)
  List<CardTypeOption> get allCardTypes {
    final List<CardTypeOption> allTypes = [];

    // إضافة الأنواع المخصصة فقط
    for (final customType in _customCardTypes) {
      allTypes.add(CardTypeOption.fromCustom(customType));
    }

    return allTypes;
  }

  Future<void> loadCustomCardTypes() async {
    _isLoading = true;
    notifyListeners();

    try {
      _customCardTypes = await _dbHelper.getAllCustomCardTypes();
    } catch (e) {
      debugPrint('Error loading custom card types: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addCustomCardType(String displayName) async {
    try {
      final now = DateTime.now();
      final customCardType = CustomCardType(
        name: displayName.toLowerCase().replaceAll(' ', '_'),
        displayName: displayName,
        createdAt: now,
        updatedAt: now,
      );

      final id = await _dbHelper.insertCustomCardType(customCardType);
      final newCardType = customCardType.copyWith(id: id);

      _customCardTypes.add(newCardType);
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding custom card type: $e');
      rethrow;
    }
  }

  Future<void> updateCustomCardType(CustomCardType cardType) async {
    try {
      final updatedCardType = cardType.copyWith(updatedAt: DateTime.now());
      await _dbHelper.updateCustomCardType(updatedCardType);

      final index = _customCardTypes.indexWhere((ct) => ct.id == cardType.id);
      if (index != -1) {
        _customCardTypes[index] = updatedCardType;
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating custom card type: $e');
      rethrow;
    }
  }

  Future<void> deleteCustomCardType(int id) async {
    try {
      await _dbHelper.deleteCustomCardType(id);
      _customCardTypes.removeWhere((ct) => ct.id == id);
      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting custom card type: $e');
      rethrow;
    }
  }

  // التحقق من وجود نوع كارت مخصص بنفس الاسم
  bool isCustomCardTypeExists(String displayName) {
    return _customCardTypes.any(
      (ct) => ct.displayName.toLowerCase() == displayName.toLowerCase(),
    );
  }

  // الحصول على نوع الكارت بواسطة المعرف
  CardTypeOption? getCardTypeById(String id) {
    try {
      return allCardTypes.firstWhere((ct) => ct.id == id);
    } catch (e) {
      // إذا لم يتم العثور على نوع الكارت، ارجع null
      return null;
    }
  }
}
