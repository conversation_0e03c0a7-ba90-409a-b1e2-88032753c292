import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../providers/card_profit_provider.dart';
import '../providers/card_type_provider.dart';
import '../models/card_profit.dart';
import '../utils/number_formatter.dart';

class CardProfitScreen extends StatefulWidget {
  const CardProfitScreen({super.key});

  @override
  State<CardProfitScreen> createState() => _CardProfitScreenState();
}

class _CardProfitScreenState extends State<CardProfitScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<CardProfitProvider>(context, listen: false).loadProfits();
      Provider.of<CardTypeProvider>(
        context,
        listen: false,
      ).loadCustomCardTypes();
      _addSampleDataIfEmpty();
    });
  }

  // إضافة بيانات تجريبية إذا كانت قائمة الأرباح فارغة
  void _addSampleDataIfEmpty() async {
    final profitProvider = Provider.of<CardProfitProvider>(
      context,
      listen: false,
    );

    // انتظار تحميل البيانات
    await Future.delayed(const Duration(milliseconds: 500));

    if (profitProvider.profits.isEmpty) {
      // إضافة بيانات تجريبية
      final sampleProfits = [
        CardProfit(
          cardType: 'زين',
          costPrice: 6500,
          sellingPrice: 6750,
          quantity: 10,
          date: DateTime.now().subtract(const Duration(days: 1)),
        ),
        CardProfit(
          cardType: 'آسيا',
          costPrice: 7500,
          sellingPrice: 7750,
          quantity: 5,
          date: DateTime.now().subtract(const Duration(days: 2)),
        ),
        CardProfit(
          cardType: 'أبو العشرة',
          costPrice: 9800,
          sellingPrice: 10000,
          quantity: 3,
          date: DateTime.now().subtract(const Duration(days: 3)),
        ),
      ];

      for (final profit in sampleProfits) {
        try {
          await profitProvider.addProfit(profit);
        } catch (e) {
          debugPrint('خطأ في إضافة البيانات التجريبية: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'إدارة أرباح البطاقات',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        backgroundColor: Colors.green.shade600,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showPriceSettingsDialog(context),
            tooltip: 'إعدادات الأسعار',
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showProfitAnalytics(context),
            tooltip: 'تحليل الأرباح',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              Provider.of<CardProfitProvider>(
                context,
                listen: false,
              ).loadProfits();
            },
            tooltip: 'تحديث',
          ),
        ],
      ),
      body: Consumer<CardProfitProvider>(
        builder: (context, profitProvider, child) {
          if (profitProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (profitProvider.profits.isEmpty) {
            return _buildEmptyState();
          }

          return _buildProfitsList(profitProvider);
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddProfitDialog(context),
        backgroundColor: Colors.green.shade600,
        child: const Icon(Icons.add, color: Colors.white, size: 28),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.trending_up, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد أرباح مسجلة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على زر + لإضافة ربح جديد',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  Widget _buildProfitsList(CardProfitProvider profitProvider) {
    return Column(
      children: [
        // إجمالي الأرباح
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade600, Colors.green.shade400],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withValues(alpha: 0.3),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(
                Icons.account_balance_wallet,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إجمالي الأرباح',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      NumberFormatter.formatCurrency(
                        profitProvider.getTotalProfits(),
                      ),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // قائمة الأرباح
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: profitProvider.profits.length,
            itemBuilder: (context, index) {
              final profit = profitProvider.profits[index];
              return _buildProfitCard(profit);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfitCard(CardProfit profit) {
    // حساب عدد الأيام منذ الإضافة
    final daysSinceAdded = DateTime.now().difference(profit.createdAt).inDays;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getCardTypeColor(profit.cardType),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    profit.cardType,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const Spacer(),
                // عداد الأيام
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.shade300),
                  ),
                  child: Text(
                    'منذ $daysSinceAdded يوم',
                    style: TextStyle(
                      color: Colors.blue.shade700,
                      fontSize: 11,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // زر الحذف
                IconButton(
                  icon: Icon(
                    Icons.delete,
                    color: Colors.red.shade600,
                    size: 20,
                  ),
                  onPressed: () => _showDeleteConfirmation(profit),
                  tooltip: 'حذف',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(
                    minWidth: 32,
                    minHeight: 32,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            // تاريخ الإضافة
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 14,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 4),
                Text(
                  'تاريخ الإضافة: ${DateFormat('yyyy/MM/dd').format(profit.date)}',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'سعر الشراء',
                    NumberFormatter.formatCurrency(profit.costPrice),
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'سعر البيع',
                    NumberFormatter.formatCurrency(profit.sellingPrice),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('الكمية', '${profit.quantity} بطاقة'),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'إجمالي الربح',
                    NumberFormatter.formatCurrency(profit.totalProfit),
                    isProfit: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, {bool isProfit = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: isProfit ? Colors.green.shade600 : Colors.black87,
          ),
        ),
      ],
    );
  }

  Color _getCardTypeColor(String cardType) {
    switch (cardType.toLowerCase()) {
      case 'زين':
        return Colors.purple;
      case 'آسيا':
        return Colors.red;
      case 'أبو العشرة':
        return Colors.teal;
      case 'أبو الستة':
        return Colors.blue;
      case 'نقدي':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  // الحصول على قائمة فريدة من أنواع البطاقات
  List<DropdownMenuItem<String>> _getUniqueCardTypes(
    CardTypeProvider cardTypeProvider,
  ) {
    // قائمة أنواع البطاقات - فقط الأنواع المخصصة
    final cardTypes = <String>{};

    // إضافة الأنواع المخصصة فقط
    try {
      for (final cardType in cardTypeProvider.allCardTypes) {
        if (cardType.displayName.isNotEmpty) {
          cardTypes.add(cardType.displayName);
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل أنواع البطاقات: $e');
    }

    // تحويل إلى قائمة مرتبة
    final sortedTypes = cardTypes.toList()..sort();

    return sortedTypes.map((cardType) {
      return DropdownMenuItem<String>(
        value: cardType,
        child: Text(cardType, style: const TextStyle(fontSize: 16)),
      );
    }).toList();
  }

  void _showAddProfitDialog(BuildContext context) {
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );
    String? selectedCardType;
    final costPriceController = TextEditingController();
    final sellingPriceController = TextEditingController();
    final quantityController = TextEditingController();
    DateTime selectedDate = DateTime.now();

    // تحميل أنواع البطاقات مسبق<|im_start|>
    final availableCardTypes = _getUniqueCardTypes(cardTypeProvider);

    // التأكد من وجود أنواع بطاقات
    if (availableCardTypes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد أنواع بطاقات متاحة'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.trending_up, color: Colors.green),
              SizedBox(width: 8),
              Text(
                'إضافة ربح جديد',
                style: TextStyle(
                  color: Colors.black87, // لون أسود ثابت للعنوان لضمان الوضوح
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اختيار نوع البطاقة
                DropdownButtonFormField<String>(
                  value: selectedCardType,
                  decoration: const InputDecoration(
                    labelText: 'نوع البطاقة',
                    border: OutlineInputBorder(),
                  ),
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  dropdownColor: Colors.white, // خلفية بيضاء للقائمة المنسدلة
                  items: availableCardTypes,
                  onChanged: (value) {
                    setState(() {
                      selectedCardType = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // سعر الشراء
                TextField(
                  controller: costPriceController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  decoration: const InputDecoration(
                    labelText: 'سعر الشراء',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.money_off),
                  ),
                ),
                const SizedBox(height: 16),

                // سعر البيع
                TextField(
                  controller: sellingPriceController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  decoration: const InputDecoration(
                    labelText: 'سعر البيع',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.attach_money),
                  ),
                ),
                const SizedBox(height: 16),

                // الكمية
                TextField(
                  controller: quantityController,
                  keyboardType: TextInputType.number,
                  style: const TextStyle(
                    color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                    fontSize: 16,
                  ),
                  decoration: const InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.numbers),
                  ),
                ),
                const SizedBox(height: 16),

                // التاريخ
                ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: Text(
                    'التاريخ: ${DateFormat('yyyy/MM/dd', 'en').format(selectedDate)}',
                    style: const TextStyle(
                      color: Colors.black87, // لون أسود ثابت للنص لضمان الوضوح
                      fontSize: 16,
                    ),
                  ),
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: selectedDate,
                      firstDate: DateTime(2020),
                      lastDate: DateTime.now(),
                    );
                    if (picked != null) {
                      setState(() {
                        selectedDate = picked;
                      });
                    }
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (selectedCardType != null &&
                    costPriceController.text.isNotEmpty &&
                    sellingPriceController.text.isNotEmpty &&
                    quantityController.text.isNotEmpty) {
                  try {
                    final costPrice = double.parse(costPriceController.text);
                    final sellingPrice = double.parse(
                      sellingPriceController.text,
                    );
                    final quantity = int.parse(quantityController.text);

                    final profit = CardProfit(
                      cardType: selectedCardType!,
                      costPrice: costPrice,
                      sellingPrice: sellingPrice,
                      quantity: quantity,
                      date: selectedDate,
                    );

                    final profitProvider = Provider.of<CardProfitProvider>(
                      context,
                      listen: false,
                    );
                    await profitProvider.addProfit(profit);

                    if (context.mounted) {
                      Navigator.pop(context);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('تم إضافة الربح بنجاح'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    }
                  } catch (e) {
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('خطأ: ${e.toString()}'),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('إضافة'),
            ),
          ],
        ),
      ),
    );
  }

  void _showPriceSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.settings, color: Colors.green),
            SizedBox(width: 8),
            Text('إعدادات أسعار الشراء'),
          ],
        ),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'أسعار الشراء الافتراضية المستخدمة في حساب الأرباح التلقائية:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('• زين: 6,500 دينار'),
              Text('• آسيا: 7,500 دينار'),
              Text('• أبو العشرة: 9,800 دينار'),
              Text('• أبو الستة: 4,800 دينار'),
              Text('• نقدي: 0 دينار (لا توجد تكلفة)'),
              SizedBox(height: 16),
              Text(
                'ملاحظة: يتم حساب الربح تلقائياً عند إضافة دين جديد بناءً على هذه الأسعار.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showProfitAnalytics(BuildContext context) {
    final profitProvider = Provider.of<CardProfitProvider>(
      context,
      listen: false,
    );
    final topCards = profitProvider.getTopProfitableCards();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue),
            SizedBox(width: 8),
            Text('تحليل الأرباح'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إجمالي الأرباح: ${NumberFormatter.formatCurrency(profitProvider.getTotalProfits())}',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.green,
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'أفضل البطاقات ربحاً:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...topCards
                  .take(3)
                  .map(
                    (card) => Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Row(
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: _getCardTypeColor(card['cardType']),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${card['cardType']}: ${NumberFormatter.formatCurrency(card['totalProfit'])}',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  // دالة إظهار تأكيد الحذف
  void _showDeleteConfirmation(CardProfit profit) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          title: const Text(
            'تأكيد الحذف',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          content: Text(
            'هل أنت متأكد من حذف بطاقة "${profit.cardType}"؟\n\nسيتم حذف جميع بيانات الربح المرتبطة بها.',
            style: const TextStyle(color: Colors.black87),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _deleteProfit(profit);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade600,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  // دالة حذف الربح
  Future<void> _deleteProfit(CardProfit profit) async {
    try {
      final profitProvider = Provider.of<CardProfitProvider>(
        context,
        listen: false,
      );
      await profitProvider.deleteProfit(profit.id!);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم حذف بطاقة "${profit.cardType}" بنجاح'),
            backgroundColor: Colors.green.shade600,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف البطاقة: $e'),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
