import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FormDataProvider extends ChangeNotifier {
  String _lastQuantity = '1';
  String _lastAmount = '';
  String _lastNotes = '';
  bool _isLoaded = false;

  // Quick values lists - will be loaded from storage or use defaults
  List<int> _quickQuantities = [];
  List<String> _quickNotes = [];
  List<double> _quickAmounts = [];

  String get lastQuantity => _lastQuantity;
  String get lastAmount => _lastAmount;
  String get lastNotes => _lastNotes;
  bool get isLoaded => _isLoaded;

  List<int> get quickQuantities => _quickQuantities;
  List<String> get quickNotes => _quickNotes;
  List<double> get quickAmounts => _quickAmounts;

  // Load saved form data
  Future<void> loadFormData() async {
    if (_isLoaded) return; // Avoid loading multiple times

    try {
      final prefs = await SharedPreferences.getInstance();
      _lastQuantity = prefs.getString('form_last_quantity') ?? '1';
      _lastAmount = prefs.getString('form_last_amount') ?? '';
      _lastNotes = prefs.getString('form_last_notes') ?? '';

      // Load quick values - ALWAYS load from storage, never reset
      final quickQuantitiesJson = prefs.getStringList('quick_quantities_permanent');
      if (quickQuantitiesJson != null && quickQuantitiesJson.isNotEmpty) {
        try {
          _quickQuantities = quickQuantitiesJson.map((e) => int.parse(e)).toList();
          debugPrint('FormDataProvider: Loaded saved quantities: $_quickQuantities');
        } catch (e) {
          debugPrint('FormDataProvider: Error parsing quantities, using defaults');
          _quickQuantities = [1, 5, 10, 20, 50];
        }
      } else {
        // First time only - set defaults and save them permanently
        _quickQuantities = [1, 5, 10, 20, 50];
        await _saveQuickValuesPermanently();
        debugPrint('FormDataProvider: First time setup - saved default quantities');
      }

      final quickNotesJson = prefs.getStringList('quick_notes_permanent');
      if (quickNotesJson != null && quickNotesJson.isNotEmpty) {
        _quickNotes = quickNotesJson;
        debugPrint('FormDataProvider: Loaded saved notes: $_quickNotes');
      } else {
        // First time only - set defaults and save them permanently
        _quickNotes = ['دين عادي', 'دين مستعجل', 'دين مؤجل'];
        await _saveQuickValuesPermanently();
        debugPrint('FormDataProvider: First time setup - saved default notes');
      }

      final quickAmountsJson = prefs.getStringList('quick_amounts_permanent');
      if (quickAmountsJson != null && quickAmountsJson.isNotEmpty) {
        try {
          _quickAmounts = quickAmountsJson.map((e) => double.parse(e)).toList();
          debugPrint('FormDataProvider: Loaded saved amounts: $_quickAmounts');
        } catch (e) {
          debugPrint('FormDataProvider: Error parsing amounts, using defaults');
          _quickAmounts = [6750, 7750];
        }
      } else {
        // First time only - set defaults and save them permanently
        _quickAmounts = [6750, 7750];
        await _saveQuickValuesPermanently();
        debugPrint('FormDataProvider: First time setup - saved default amounts');
      }

      _isLoaded = true;

      debugPrint('FormDataProvider: FINAL LOADED VALUES:');
      debugPrint('  - Quantities: $_quickQuantities');
      debugPrint('  - Notes: $_quickNotes');
      debugPrint('  - Amounts: $_quickAmounts');

      notifyListeners();
    } catch (e) {
      debugPrint('FormDataProvider: Error loading form data: $e');
      // Fallback to defaults if everything fails
      _quickQuantities = [1, 5, 10, 20, 50];
      _quickNotes = ['دين عادي', 'دين مستعجل', 'دين مؤجل'];
      _quickAmounts = [6750, 7750];
      _isLoaded = true;
      notifyListeners();
    }
  }

  // Save form data
  Future<void> saveFormData({
    required String quantity,
    required String amount,
    required String notes,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Only save if values are not empty (except quantity which defaults to '1')
      final quantityToSave = quantity.isEmpty ? '1' : quantity;
      
      await prefs.setString('form_last_quantity', quantityToSave);
      await prefs.setString('form_last_amount', amount);
      await prefs.setString('form_last_notes', notes);
      
      _lastQuantity = quantityToSave;
      _lastAmount = amount;
      _lastNotes = notes;
      
      debugPrint('FormDataProvider: Saved - quantity: $_lastQuantity, amount: $_lastAmount, notes: $_lastNotes');
      notifyListeners();
    } catch (e) {
      debugPrint('FormDataProvider: Error saving form data: $e');
    }
  }

  // Update quantity
  void updateQuantity(String quantity) {
    _lastQuantity = quantity.isEmpty ? '1' : quantity;
    notifyListeners();
    _saveToStorage();
  }

  // Update amount
  void updateAmount(String amount) {
    _lastAmount = amount;
    notifyListeners();
    _saveToStorage();
  }

  // Update notes
  void updateNotes(String notes) {
    _lastNotes = notes;
    notifyListeners();
    _saveToStorage();
  }

  // Private method to save to storage
  void _saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('form_last_quantity', _lastQuantity);
      await prefs.setString('form_last_amount', _lastAmount);
      await prefs.setString('form_last_notes', _lastNotes);
    } catch (e) {
      debugPrint('FormDataProvider: Error saving to storage: $e');
    }
  }

  // Quick values management
  Future<void> addQuickQuantity(int quantity) async {
    if (!_quickQuantities.contains(quantity)) {
      _quickQuantities.add(quantity);
      _quickQuantities.sort();
      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: Added quantity $quantity, new list: $_quickQuantities');
      notifyListeners();
    }
  }

  Future<void> removeQuickQuantity(int quantity) async {
    // Prevent removing if only minimum values remain
    if (_quickQuantities.length > 3) {
      _quickQuantities.remove(quantity);
      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: Removed quantity $quantity, new list: $_quickQuantities');
      notifyListeners();
    } else {
      debugPrint('FormDataProvider: Cannot remove quantity - minimum 3 values required');
    }
  }

  Future<void> addQuickNote(String note) async {
    if (!_quickNotes.contains(note)) {
      _quickNotes.add(note);
      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: Added note "$note", new list: $_quickNotes');
      notifyListeners();
    }
  }

  Future<void> removeQuickNote(String note) async {
    // Prevent removing if only minimum values remain
    if (_quickNotes.length > 3) {
      _quickNotes.remove(note);
      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: Removed note "$note", new list: $_quickNotes');
      notifyListeners();
    } else {
      debugPrint('FormDataProvider: Cannot remove note - minimum 3 values required');
    }
  }

  Future<void> addQuickAmount(double amount) async {
    if (!_quickAmounts.contains(amount)) {
      _quickAmounts.add(amount);
      _quickAmounts.sort();
      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: Added amount $amount, new list: $_quickAmounts');
      notifyListeners();
    }
  }

  Future<void> removeQuickAmount(double amount) async {
    // Prevent removing if only minimum values remain
    if (_quickAmounts.length > 2) {
      _quickAmounts.remove(amount);
      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: Removed amount $amount, new list: $_quickAmounts');
      notifyListeners();
    } else {
      debugPrint('FormDataProvider: Cannot remove amount - minimum 2 values required');
    }
  }

  // Save quick values to storage PERMANENTLY (never reset)
  Future<void> _saveQuickValuesPermanently() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setStringList('quick_quantities_permanent', _quickQuantities.map((e) => e.toString()).toList());
      await prefs.setStringList('quick_notes_permanent', _quickNotes);
      await prefs.setStringList('quick_amounts_permanent', _quickAmounts.map((e) => e.toString()).toList());
      debugPrint('FormDataProvider: Quick values saved PERMANENTLY');
      debugPrint('  - Saved quantities: $_quickQuantities');
      debugPrint('  - Saved notes: $_quickNotes');
      debugPrint('  - Saved amounts: $_quickAmounts');
    } catch (e) {
      debugPrint('FormDataProvider: Error saving quick values permanently: $e');
    }
  }



  // Reset quick values to defaults (ONLY if user explicitly requests it)
  Future<void> resetQuickValuesToDefaults() async {
    try {
      _quickQuantities = [1, 5, 10, 20, 50];
      _quickNotes = ['دين عادي', 'دين مستعجل', 'دين مؤجل'];
      _quickAmounts = [6750, 7750];

      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: Reset quick values to defaults');
      notifyListeners();
    } catch (e) {
      debugPrint('FormDataProvider: Error resetting quick values: $e');
    }
  }

  // Clear ONLY form data (NOT quick values - they stay permanent)
  Future<void> clearFormData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('form_last_quantity');
      await prefs.remove('form_last_amount');
      await prefs.remove('form_last_notes');

      // DO NOT remove permanent quick values
      // await prefs.remove('quick_quantities_permanent');
      // await prefs.remove('quick_notes_permanent');
      // await prefs.remove('quick_amounts_permanent');

      _lastQuantity = '1';
      _lastAmount = '';
      _lastNotes = '';

      // DO NOT reset quick values - they stay as they are

      debugPrint('FormDataProvider: Cleared form data (kept quick values)');
      notifyListeners();
    } catch (e) {
      debugPrint('FormDataProvider: Error clearing form data: $e');
    }
  }

  // EMERGENCY ONLY: Complete reset including quick values
  Future<void> emergencyResetAll() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('form_last_quantity');
      await prefs.remove('form_last_amount');
      await prefs.remove('form_last_notes');
      await prefs.remove('quick_quantities_permanent');
      await prefs.remove('quick_notes_permanent');
      await prefs.remove('quick_amounts_permanent');

      _lastQuantity = '1';
      _lastAmount = '';
      _lastNotes = '';
      _quickQuantities = [1, 5, 10, 20, 50];
      _quickNotes = ['دين عادي', 'دين مستعجل', 'دين مؤجل'];
      _quickAmounts = [6750, 7750];

      await _saveQuickValuesPermanently();
      debugPrint('FormDataProvider: EMERGENCY RESET - All data cleared');
      notifyListeners();
    } catch (e) {
      debugPrint('FormDataProvider: Error in emergency reset: $e');
    }
  }
}
