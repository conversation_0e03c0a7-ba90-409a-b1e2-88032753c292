import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/font_settings.dart';

class FontProvider extends ChangeNotifier {
  FontSettings _settings = FontSettings();
  
  FontSettings get settings => _settings;

  // Load settings from SharedPreferences
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('font_settings');
      
      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson);
        _settings = FontSettings.fromJson(settingsMap);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading font settings: $e');
    }
  }

  // Update font size
  Future<void> updateFontSize(double fontSize) async {
    _settings = _settings.copyWith(fontSize: fontSize);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  // Update font family
  Future<void> updateFontFamily(String fontFamily) async {
    _settings = _settings.copyWith(fontFamily: fontFamily);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  // Update font weight
  Future<void> updateFontWeight(String fontWeight) async {
    _settings = _settings.copyWith(fontWeight: fontWeight);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  // Update line height
  Future<void> updateLineHeight(double lineHeight) async {
    _settings = _settings.copyWith(lineHeight: lineHeight);
    notifyListeners();
    await _saveSettingsToStorage();
  }

  // Reset to default settings
  Future<void> resetToDefault() async {
    _settings = FontSettings();
    notifyListeners();
    await _saveSettingsToStorage();
  }

  // Private method to save settings to storage
  Future<void> _saveSettingsToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = json.encode(_settings.toJson());
      await prefs.setString('font_settings', settingsJson);
    } catch (e) {
      debugPrint('Error saving font settings: $e');
    }
  }

  // Get TextStyle with current settings
  TextStyle getTextStyle({
    double? baseFontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    TextDecoration? decoration,
  }) {
    return TextStyle(
      fontSize: (baseFontSize ?? 14) * _settings.fontSize,
      fontFamily: _settings.fontFamily,
      fontWeight: fontWeight ?? (_settings.fontWeight == 'bold' ? FontWeight.bold : FontWeight.normal),
      height: _settings.lineHeight,
      color: color,
      letterSpacing: letterSpacing,
      decoration: decoration,
    );
  }

  // Get Theme with current font settings
  ThemeData getTheme(ThemeData baseTheme) {
    return baseTheme.copyWith(
      textTheme: baseTheme.textTheme.copyWith(
        displayLarge: getTextStyle(baseFontSize: 57),
        displayMedium: getTextStyle(baseFontSize: 45),
        displaySmall: getTextStyle(baseFontSize: 36),
        headlineLarge: getTextStyle(baseFontSize: 32),
        headlineMedium: getTextStyle(baseFontSize: 28),
        headlineSmall: getTextStyle(baseFontSize: 24),
        titleLarge: getTextStyle(baseFontSize: 22),
        titleMedium: getTextStyle(baseFontSize: 16),
        titleSmall: getTextStyle(baseFontSize: 14),
        bodyLarge: getTextStyle(baseFontSize: 16),
        bodyMedium: getTextStyle(baseFontSize: 14),
        bodySmall: getTextStyle(baseFontSize: 12),
        labelLarge: getTextStyle(baseFontSize: 14),
        labelMedium: getTextStyle(baseFontSize: 12),
        labelSmall: getTextStyle(baseFontSize: 11),
      ),
      primaryTextTheme: baseTheme.primaryTextTheme.copyWith(
        displayLarge: getTextStyle(baseFontSize: 57),
        displayMedium: getTextStyle(baseFontSize: 45),
        displaySmall: getTextStyle(baseFontSize: 36),
        headlineLarge: getTextStyle(baseFontSize: 32),
        headlineMedium: getTextStyle(baseFontSize: 28),
        headlineSmall: getTextStyle(baseFontSize: 24),
        titleLarge: getTextStyle(baseFontSize: 22),
        titleMedium: getTextStyle(baseFontSize: 16),
        titleSmall: getTextStyle(baseFontSize: 14),
        bodyLarge: getTextStyle(baseFontSize: 16),
        bodyMedium: getTextStyle(baseFontSize: 14),
        bodySmall: getTextStyle(baseFontSize: 12),
        labelLarge: getTextStyle(baseFontSize: 14),
        labelMedium: getTextStyle(baseFontSize: 12),
        labelSmall: getTextStyle(baseFontSize: 11),
      ),
    );
  }
}
