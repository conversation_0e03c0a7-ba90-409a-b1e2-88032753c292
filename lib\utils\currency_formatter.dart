import 'package:intl/intl.dart';

class CurrencyFormatter {
  static final NumberFormat _formatter = NumberFormat('#,##0.00', 'en');

  static String format(double amount) {
    return '${_formatter.format(amount)} د.ع';
  }

  static String formatWithoutDecimals(double amount) {
    final NumberFormat formatter = NumberFormat('#,##0', 'en');
    return '${formatter.format(amount)} د.ع';
  }
  
  static String formatSmart(double amount) {
    // إذا كان الرقم صحيح (بدون كسور) نعرضه بدون .00
    if (amount == amount.roundToDouble()) {
      return formatWithoutDecimals(amount);
    } else {
      return format(amount);
    }
  }
}
