@echo off
echo ========================================
echo    Flutter Auto Reload Script
echo ========================================
echo.

:LOOP
echo [%TIME%] Checking for file changes...

REM Save all files in VS Code (if running)
tasklist /FI "IMAGENAME eq Code.exe" 2>NUL | find /I /N "Code.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo VS Code is running - files will auto-save
)

REM Check if flutter is running
tasklist /FI "IMAGENAME eq flutter.exe" 2>NUL | find /I /N "flutter.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo Flutter is running - performing hot reload
    echo r | flutter attach
) else (
    echo Flutter is not running - starting flutter run
    start /B flutter run --hot
)

REM Wait 5 seconds before next check
timeout /t 5 /nobreak >nul
goto LOOP
