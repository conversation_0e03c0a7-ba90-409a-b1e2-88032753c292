<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>App Icon Generator</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
            direction: rtl;
        }
        
        .icon-container {
            width: 512px;
            height: 512px;
            margin: 20px auto;
            position: relative;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 50%;
            border: 8px solid white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        
        .calculator {
            width: 200px;
            height: 240px;
            background: white;
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            position: relative;
        }
        
        .display {
            width: 100%;
            height: 50px;
            background: #2c3e50;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00ff88;
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }
        
        .btn {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            border: 1px solid #dee2e6;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: #495057;
        }
        
        .btn.operator {
            background: #007bff;
            color: white;
        }
        
        .btn.equals {
            background: #28a745;
            color: white;
        }
        
        .credit-card {
            position: absolute;
            top: 80px;
            right: 60px;
            width: 50px;
            height: 32px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            border-radius: 5px;
            border: 2px solid white;
        }
        
        .card-stripe {
            width: 40px;
            height: 6px;
            background: rgba(255,255,255,0.8);
            margin: 8px auto 4px;
            border-radius: 1px;
        }
        
        .card-dots {
            display: flex;
            justify-content: center;
            gap: 3px;
        }
        
        .dot {
            width: 3px;
            height: 3px;
            background: white;
            border-radius: 50%;
        }
        
        .coins {
            position: absolute;
            bottom: 120px;
            right: 40px;
        }
        
        .coin {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            border-radius: 50%;
            border: 2px solid white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: bold;
            color: #8B4513;
            margin-bottom: 5px;
        }
        
        .coin:last-child {
            width: 25px;
            height: 25px;
            opacity: 0.8;
            margin-right: 15px;
        }
        
        .title {
            position: absolute;
            bottom: 60px;
            width: 100%;
            text-align: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .subtitle {
            position: absolute;
            bottom: 30px;
            width: 100%;
            text-align: center;
            color: rgba(255,255,255,0.9);
            font-size: 16px;
        }
        
        .download-btn {
            margin: 20px auto;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="icon-container" id="icon">
        <div class="calculator">
            <div class="display">123,456</div>
            <div class="buttons">
                <div class="btn">7</div>
                <div class="btn">8</div>
                <div class="btn">9</div>
                <div class="btn operator">÷</div>
                <div class="btn">4</div>
                <div class="btn">5</div>
                <div class="btn">6</div>
                <div class="btn operator">×</div>
                <div class="btn">1</div>
                <div class="btn">2</div>
                <div class="btn">3</div>
                <div class="btn equals">=</div>
                <div class="btn" style="grid-column: span 2;">0</div>
                <div class="btn">.</div>
                <div class="btn operator">+</div>
            </div>
        </div>
        
        <div class="credit-card">
            <div class="card-stripe"></div>
            <div class="card-dots">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        </div>
        
        <div class="coins">
            <div class="coin">د.ع</div>
            <div class="coin">د.ع</div>
        </div>
        
        <div class="title">محاسب ديون</div>
        <div class="subtitle">احترافي</div>
    </div>
    
    <button class="download-btn" onclick="downloadIcon()">تحميل الأيقونة</button>
    
    <script>
        function downloadIcon() {
            // This would require html2canvas library for actual implementation
            alert('لتحميل الأيقونة، يرجى أخذ لقطة شاشة للأيقونة أعلاه');
        }
    </script>
</body>
</html>
