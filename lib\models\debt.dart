enum DebtStatus { pending, paid, partiallyPaid }

class Debt {
  Debt({
    this.id,
    required this.customerId,
    required this.itemName,
    required this.quantity,
    required this.amount,
    this.paidAmount = 0.0,
    required this.cardType,
    this.notes,
    required this.entryDate,
    required this.dueDate,
    required this.createdAt,
    required this.updatedAt,
    this.status = DebtStatus.pending,
    this.firstPaymentDate, // تاريخ أول تسديد - يحفظ مرة واحدة فقط
  });

  factory Debt.fromMap(Map<String, dynamic> map) {
    return Debt(
      id: map['id']?.toInt(),
      customerId: map['customer_id']?.toInt() ?? 0,
      itemName: map['item_name'] ?? '',
      quantity: map['quantity']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      paidAmount: map['paid_amount']?.toDouble() ?? 0.0,
      cardType: _parseCardType(map['card_type']), // Handle both int and String
      notes: map['notes'],
      entryDate: DateTime.fromMillisecondsSinceEpoch(map['entry_date']),
      dueDate: DateTime.fromMillisecondsSinceEpoch(map['due_date']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
      status: _parseDebtStatus(map['status']),
      firstPaymentDate: map['first_payment_date'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['first_payment_date'])
          : null,
    );
  }

  // Helper method to parse card type from both int and String
  static String _parseCardType(dynamic cardType) {
    if (cardType == null) return 'cash';

    if (cardType is String) {
      return cardType;
    }

    if (cardType is int) {
      // Convert old int values to new string values
      switch (cardType) {
        case 0:
          return 'cash';
        case 1:
          return 'visa';
        case 2:
          return 'mastercard';
        case 3:
          return 'mada';
        default:
          return 'cash';
      }
    }

    return 'cash'; // Default fallback
  }

  // Helper method to parse debt status safely
  static DebtStatus _parseDebtStatus(dynamic status) {
    if (status == null) return DebtStatus.pending;

    if (status is int) {
      if (status >= 0 && status < DebtStatus.values.length) {
        return DebtStatus.values[status];
      }
    }

    return DebtStatus.pending; // Default fallback
  }

  final int? id;
  final int customerId;
  final String itemName;
  final int quantity;
  final double amount;
  final double paidAmount;
  final String cardType; // Changed from CardType to String
  final String? notes;
  final DateTime entryDate;
  final DateTime dueDate;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DebtStatus status;
  final DateTime? firstPaymentDate; // تاريخ أول تسديد - يحفظ مرة واحدة فقط

  double get remainingAmount => amount - paidAmount;
  bool get isPaid => status == DebtStatus.paid;
  bool get isPartiallyPaid => status == DebtStatus.partiallyPaid;
  bool get isPending => status == DebtStatus.pending;
  bool get isOverdue => DateTime.now().isAfter(dueDate) && !isPaid;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'item_name': itemName,
      'quantity': quantity,
      'amount': amount,
      'paid_amount': paidAmount,
      'card_type': cardType, // Changed to use String directly
      'notes': notes,
      'entry_date': entryDate.millisecondsSinceEpoch,
      'due_date': dueDate.millisecondsSinceEpoch,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'status': status.index,
      'first_payment_date': firstPaymentDate?.millisecondsSinceEpoch,
    };
  }

  Debt copyWith({
    int? id,
    int? customerId,
    String? itemName,
    int? quantity,
    double? amount,
    double? paidAmount,
    String? cardType, // Changed from CardType to String
    String? notes,
    DateTime? entryDate,
    DateTime? dueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
    DebtStatus? status,
    DateTime? firstPaymentDate,
  }) {
    return Debt(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      itemName: itemName ?? this.itemName,
      quantity: quantity ?? this.quantity,
      amount: amount ?? this.amount,
      paidAmount: paidAmount ?? this.paidAmount,
      cardType: cardType ?? this.cardType,
      notes: notes ?? this.notes,
      entryDate: entryDate ?? this.entryDate,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      status: status ?? this.status,
      firstPaymentDate: firstPaymentDate ?? this.firstPaymentDate,
    );
  }

  @override
  String toString() {
    return 'Debt(id: $id, customerId: $customerId, itemName: $itemName, quantity: $quantity, amount: $amount, paidAmount: $paidAmount, cardType: $cardType, notes: $notes, entryDate: $entryDate, dueDate: $dueDate, createdAt: $createdAt, updatedAt: $updatedAt, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Debt &&
        other.id == id &&
        other.customerId == customerId &&
        other.itemName == itemName &&
        other.quantity == quantity &&
        other.amount == amount &&
        other.paidAmount == paidAmount &&
        other.cardType == cardType &&
        other.notes == notes &&
        other.entryDate == entryDate &&
        other.dueDate == dueDate &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        itemName.hashCode ^
        quantity.hashCode ^
        amount.hashCode ^
        paidAmount.hashCode ^
        cardType.hashCode ^
        notes.hashCode ^
        entryDate.hashCode ^
        dueDate.hashCode ^
        createdAt.hashCode ^
        updatedAt.hashCode ^
        status.hashCode;
  }
}

extension DebtStatusExtension on DebtStatus {
  String get displayName {
    switch (this) {
      case DebtStatus.pending:
        return 'معلق';
      case DebtStatus.paid:
        return 'مدفوع';
      case DebtStatus.partiallyPaid:
        return 'مدفوع جزئياً';
    }
  }
}
