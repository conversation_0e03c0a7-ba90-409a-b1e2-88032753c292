@echo off
title Flutter Development Environment
color 0A

echo.
echo ========================================
echo    Flutter Development Environment
echo ========================================
echo.

REM Check if Flutter is installed
flutter --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ❌ Flutter is not installed or not in PATH
    echo Please install Flutter and try again
    pause
    exit /b 1
)

echo ✅ Flutter detected
echo.

REM Clean and get dependencies
echo 📦 Cleaning and getting dependencies...
flutter clean
flutter pub get

REM Check for device
echo 📱 Checking for connected devices...
flutter devices

REM Start Flutter with hot reload
echo 🚀 Starting Flutter with hot reload...
echo.
echo 💡 Tips:
echo    - Press 'r' for hot reload
echo    - Press 'R' for hot restart
echo    - Press 'q' to quit
echo    - All files will auto-save
echo.

REM Start flutter run with enhanced options
flutter run --hot --enable-software-rendering --verbose

echo.
echo Development session ended.
pause
