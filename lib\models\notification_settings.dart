class NotificationSettings {
  // Constructor
  NotificationSettings({
    this.salesNotificationsEnabled = true,
    this.inventoryNotificationsEnabled = true,
    this.notificationDuration = 3,
    this.lowStockThreshold = 10,
    this.dailyInventoryReminder = true,
    this.reminderTime = '09:00',
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      salesNotificationsEnabled: json['salesNotificationsEnabled'] ?? true,
      inventoryNotificationsEnabled: json['inventoryNotificationsEnabled'] ?? true,
      notificationDuration: json['notificationDuration'] ?? 3,
      lowStockThreshold: json['lowStockThreshold'] ?? 10,
      dailyInventoryReminder: json['dailyInventoryReminder'] ?? true,
      reminderTime: json['reminderTime'] ?? '09:00',
    );
  }

  // Fields
  final bool salesNotificationsEnabled;
  final bool inventoryNotificationsEnabled;
  final int notificationDuration; // in seconds
  final int lowStockThreshold; // minimum quantity before alert
  final bool dailyInventoryReminder;
  final String reminderTime; // HH:mm format

  Map<String, dynamic> toJson() {
    return {
      'salesNotificationsEnabled': salesNotificationsEnabled,
      'inventoryNotificationsEnabled': inventoryNotificationsEnabled,
      'notificationDuration': notificationDuration,
      'lowStockThreshold': lowStockThreshold,
      'dailyInventoryReminder': dailyInventoryReminder,
      'reminderTime': reminderTime,
    };
  }

  NotificationSettings copyWith({
    bool? salesNotificationsEnabled,
    bool? inventoryNotificationsEnabled,
    int? notificationDuration,
    int? lowStockThreshold,
    bool? dailyInventoryReminder,
    String? reminderTime,
  }) {
    return NotificationSettings(
      salesNotificationsEnabled: salesNotificationsEnabled ?? this.salesNotificationsEnabled,
      inventoryNotificationsEnabled: inventoryNotificationsEnabled ?? this.inventoryNotificationsEnabled,
      notificationDuration: notificationDuration ?? this.notificationDuration,
      lowStockThreshold: lowStockThreshold ?? this.lowStockThreshold,
      dailyInventoryReminder: dailyInventoryReminder ?? this.dailyInventoryReminder,
      reminderTime: reminderTime ?? this.reminderTime,
    );
  }
}
