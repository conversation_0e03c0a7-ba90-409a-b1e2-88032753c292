import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../providers/debt_provider.dart';
import 'customer_debts_screen.dart';
import 'customer_payments_screen.dart';
import 'customer_info_screen.dart';

class CustomerDetailScreen extends StatefulWidget {
  const CustomerDetailScreen({super.key, required this.customer});
  final Customer customer;

  @override
  State<CustomerDetailScreen> createState() => _CustomerDetailScreenState();
}

class _CustomerDetailScreenState extends State<CustomerDetailScreen> {
  @override
  void initState() {
    super.initState();
    // Load customer debts and payments
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final debtProvider = Provider.of<DebtProvider>(context, listen: false);
      debtProvider.loadCustomerDebts(widget.customer.id!);
      debtProvider.loadCustomerPayments(widget.customer.id!);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh debts and payments when returning to this screen
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    if (debtProvider.currentCustomerId == widget.customer.id) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        debtProvider.refreshCurrentCustomerDebts();
        debtProvider.loadCustomerPayments(widget.customer.id!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.customer.name),
        backgroundColor: const Color(0xFF00695C),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Customer Info Card
            Container(
              margin: const EdgeInsets.all(20),
              child: _buildSimpleCustomerInfoCard(),
            ),

            // Navigation Buttons
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // Debts Card (Full Width)
                  _buildAdvancedActionCard(
                    onPressed: () async {
                      final debtProvider = Provider.of<DebtProvider>(
                        context,
                        listen: false,
                      );
                      await Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              CustomerDebtsScreen(customer: widget.customer),
                        ),
                      );
                      // Refresh payments when returning from debts screen
                      if (mounted) {
                        await debtProvider.loadCustomerPayments(
                          widget.customer.id!,
                        );
                      }
                    },
                    icon: Icons.receipt_long,
                    title: 'قائمة الديون',
                    color: Colors.orange,
                    gradient: LinearGradient(
                      colors: [Colors.grey.shade200, Colors.grey.shade400],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    isDebtCard: true,
                    isFullWidth: true,
                  ),

                  const SizedBox(height: 16),

                  // Payments Card (Full Width)
                  _buildAdvancedActionCard(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) =>
                              CustomerPaymentsScreen(customer: widget.customer),
                        ),
                      );
                    },
                    icon: Icons.payment,
                    title: 'قائمة التسديدات',
                    color: Colors.green,
                    gradient: LinearGradient(
                      colors: [Colors.grey.shade300, Colors.grey.shade500],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    isDebtCard: false,
                    isFullWidth: true,
                  ),

                  const SizedBox(height: 16),

                  // Statistics Card (Full Width)
                  _buildAdvancedStatisticsCard(
                    onPressed: () {
                      _showStatisticsBottomSheet(context);
                    },
                  ),
                ],
              ),
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // بناء بطاقة معلومات العميل المبسطة
  Widget _buildSimpleCustomerInfoCard() {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: InkWell(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) =>
                      CustomerInfoScreen(customer: widget.customer),
                ),
              );
            },
            borderRadius: BorderRadius.circular(20),
            child: Row(
              children: [
                // Icon Section
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: Icon(
                    Icons.person,
                    size: 32,
                    color: Colors.grey.shade600,
                  ),
                ),

                const SizedBox(width: 16),

                // Content Section
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'معلومات العميل',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.customer.name,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.black54,
                        ),
                      ),
                      if (widget.customer.phone != null &&
                          widget.customer.phone!.isNotEmpty) ...[
                        const SizedBox(height: 2),
                        Text(
                          widget.customer.phone!,
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.blue,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Arrow and Stats
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${customerDebts.length}',
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'عرض التفاصيل',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: Colors.grey.shade600,
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // بناء بطاقة إحصائيات متقدمة
  Widget _buildAdvancedStatisticsCard({required VoidCallback onPressed}) {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        final totalAmount = customerDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final overdueDebts = customerDebts
            .where((debt) => debt.isOverdue)
            .length;

        return Container(
          width: double.infinity,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Icon Section
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Icon(
                        Icons.analytics_outlined,
                        size: 32,
                        color: Colors.grey.shade600,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Content Section
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'إحصائيات الديون',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade800,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'إجمالي: ${_formatNumber(totalAmount)} د.ع',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Stats Section
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                overdueDebts > 0
                                    ? Icons.warning
                                    : Icons.check_circle,
                                size: 12,
                                color: overdueDebts > 0
                                    ? Colors.red
                                    : Colors.green,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                overdueDebts > 0
                                    ? '$overdueDebts متأخر'
                                    : 'منتظم',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: overdueDebts > 0
                                      ? Colors.red
                                      : Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${customerDebts.length} كارت',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // بناء بطاقة عمل متقدمة مع معلومات احترافية
  Widget _buildAdvancedActionCard({
    required VoidCallback onPressed,
    required IconData icon,
    required String title,
    required Color color,
    required Gradient gradient,
    required bool isDebtCard,
    bool isFullWidth = false,
  }) {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        // حساب الإحصائيات للعميل
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        final totalAmount = customerDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final paidAmount = customerDebts.fold(
          0.0,
          (sum, debt) => sum + debt.paidAmount,
        );
        final remainingAmount = totalAmount - paidAmount;

        final pendingDebts = customerDebts
            .where((debt) => debt.status == DebtStatus.pending)
            .length;
        final paidDebts = customerDebts
            .where((debt) => debt.status == DebtStatus.paid)
            .length;
        final overdueDebts = customerDebts
            .where((debt) => debt.isOverdue)
            .length;

        // معلومات مختلفة حسب نوع البطاقة
        String mainValue;
        String subtitle;
        String statusText;
        IconData statusIcon;
        Color statusColor;

        if (isDebtCard) {
          mainValue = '${customerDebts.length}';
          subtitle = '${remainingAmount.toStringAsFixed(0)} د.ع متبقي';
          if (overdueDebts > 0) {
            statusText = '$overdueDebts متأخر';
            statusIcon = Icons.warning;
            statusColor = Colors.red;
          } else if (pendingDebts > 0) {
            statusText = '$pendingDebts معلق';
            statusIcon = Icons.schedule;
            statusColor = Colors.orange;
          } else {
            statusText = 'مسدد بالكامل';
            statusIcon = Icons.check_circle;
            statusColor = Colors.green;
          }
        } else {
          // بطاقة التسديدات
          mainValue = '$paidDebts';
          subtitle = '${paidAmount.toStringAsFixed(0)} د.ع مسدد';
          final paymentRate = customerDebts.isNotEmpty
              ? (paidDebts / customerDebts.length * 100).toStringAsFixed(0)
              : '0';
          statusText = '$paymentRate% معدل السداد';
          statusIcon = Icons.trending_up;
          statusColor = Colors.blue;
        }

        return Container(
          width: isFullWidth ? double.infinity : null,
          height: isFullWidth ? 100 : 120,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.grey.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onPressed,
              borderRadius: BorderRadius.circular(20),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: isFullWidth
                    ? Row(
                        children: [
                          // Icon Section
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.grey.shade200),
                            ),
                            child: Icon(
                              icon,
                              size: 32,
                              color: Colors.grey.shade600,
                            ),
                          ),

                          const SizedBox(width: 16),

                          // Content Section
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  title,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  subtitle,
                                  style: const TextStyle(
                                    fontSize: 12,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Stats Section
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                mainValue,
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      statusIcon,
                                      size: 12,
                                      color: statusColor,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      statusText,
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: statusColor,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      )
                    : Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header Row
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                  ),
                                ),
                                child: Icon(
                                  icon,
                                  size: 24,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade300,
                                  ),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      statusIcon,
                                      size: 12,
                                      color: statusColor,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      statusText,
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: statusColor,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 12),

                          // Main Value
                          Text(
                            mainValue,
                            style: const TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                            ),
                          ),

                          const SizedBox(height: 4),

                          // Title and Subtitle
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Colors.black87,
                            ),
                          ),
                          Text(
                            subtitle,
                            style: const TextStyle(
                              fontSize: 11,
                              color: Colors.black54,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showStatisticsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Icon(Icons.analytics, color: Colors.blue[600], size: 24),
                  const SizedBox(width: 12),
                  Text(
                    'إحصائيات ${widget.customer.name}',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            // Statistics content
            Expanded(child: _buildStatisticsContent(context)),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsContent(BuildContext context) {
    return Consumer<DebtProvider>(
      builder: (context, debtProvider, child) {
        final customerDebts = debtProvider.debts
            .where((debt) => debt.customerId == widget.customer.id)
            .toList();

        if (customerDebts.isEmpty) {
          return const Center(
            child: Text(
              'لا توجد ديون لهذا العميل',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          );
        }

        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final yesterday = today.subtract(const Duration(days: 1));
        final weekStart = today.subtract(Duration(days: today.weekday - 1));
        final monthStart = DateTime(now.year, now.month);
        final last30Days = today.subtract(const Duration(days: 30));

        // حسابات اليوم
        final todayDebts = customerDebts.where((debt) {
          final debtDate = DateTime(
            debt.entryDate.year,
            debt.entryDate.month,
            debt.entryDate.day,
          );
          return debtDate.isAtSameMomentAs(today);
        }).toList();
        final todaySales = todayDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final todayCount = todayDebts.length;

        // حسابات الأمس
        final yesterdayDebts = customerDebts.where((debt) {
          final debtDate = DateTime(
            debt.entryDate.year,
            debt.entryDate.month,
            debt.entryDate.day,
          );
          return debtDate.isAtSameMomentAs(yesterday);
        }).toList();
        final yesterdaySales = yesterdayDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final yesterdayCount = yesterdayDebts.length;

        // حسابات الأسبوع
        final weekDebts = customerDebts
            .where((debt) => debt.entryDate.isAfter(weekStart))
            .toList();
        final weekSales = weekDebts.fold(0.0, (sum, debt) => sum + debt.amount);
        final weekCount = weekDebts.length;

        // حسابات الشهر
        final monthDebts = customerDebts
            .where((debt) => debt.entryDate.isAfter(monthStart))
            .toList();
        final monthSales = monthDebts.fold(
          0.0,
          (sum, debt) => sum + debt.amount,
        );
        final monthCount = monthDebts.length;

        // نشاط العميل (آخر 30 يوم)
        final recentDebts = customerDebts
            .where((debt) => debt.entryDate.isAfter(last30Days))
            .toList();
        final activityPercentage = customerDebts.isNotEmpty
            ? (recentDebts.length / customerDebts.length * 100)
            : 0.0;

        // إحصائيات أنواع الكروت
        final cardTypeStats = <String, int>{};
        for (final debt in customerDebts) {
          cardTypeStats[debt.cardType] =
              (cardTypeStats[debt.cardType] ?? 0) + 1;
        }
        final sortedCardTypes = cardTypeStats.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

        // الديون المتأخرة
        final overdueDebts = customerDebts.where((debt) {
          if (debt.status == DebtStatus.paid) return false;
          final dueDate = DateTime(
            debt.dueDate.year,
            debt.dueDate.month,
            debt.dueDate.day,
          );
          return dueDate.isBefore(today);
        }).toList();
        final overdueAmount = overdueDebts.fold(
          0.0,
          (sum, debt) => sum + debt.remainingAmount,
        );

        // الديون المستحقة اليوم
        final dueTodayDebts = customerDebts.where((debt) {
          if (debt.status == DebtStatus.paid) return false;
          final dueDate = DateTime(
            debt.dueDate.year,
            debt.dueDate.month,
            debt.dueDate.day,
          );
          return dueDate.isAtSameMomentAs(today);
        }).toList();
        final dueTodayAmount = dueTodayDebts.fold(
          0.0,
          (sum, debt) => sum + debt.remainingAmount,
        );

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'إحصائيات الديون',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 16),

              // الصف الأول - مبيعات اليوم والأمس
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'مبيعات اليوم',
                      _formatNumber(todaySales),
                      Icons.today,
                      Colors.blue,
                      'د.ع',
                      subtitle: '$todayCount كارت',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'مبيعات الأمس',
                      _formatNumber(yesterdaySales),
                      Icons.history,
                      Colors.teal,
                      'د.ع',
                      subtitle: '$yesterdayCount كارت',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثاني - مبيعات الأسبوع والشهر
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'مبيعات الأسبوع',
                      _formatNumber(weekSales),
                      Icons.date_range,
                      Colors.green,
                      'د.ع',
                      subtitle: '$weekCount كارت',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'مبيعات الشهر',
                      _formatNumber(monthSales),
                      Icons.calendar_month,
                      Colors.purple,
                      'د.ع',
                      subtitle: '$monthCount كارت',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الثالث - إجمالي الديون ونشاط العميل
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'إجمالي الديون',
                      _formatNumber(
                        customerDebts.fold(
                          0.0,
                          (sum, debt) => sum + debt.amount,
                        ),
                      ),
                      Icons.account_balance,
                      Colors.indigo,
                      'د.ع',
                      subtitle: '${customerDebts.length} كارت',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'نشاط العميل',
                      activityPercentage.toStringAsFixed(1),
                      Icons.trending_up,
                      Colors.cyan,
                      '%',
                      subtitle: 'آخر 30 يوم',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // الصف الرابع - الديون المتأخرة والمستحقة اليوم
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'ديون متأخرة',
                      _formatNumber(overdueAmount),
                      Icons.warning,
                      Colors.red,
                      'د.ع',
                      subtitle: '${overdueDebts.length} كارت',
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildStatCard(
                      'استحقاق اليوم',
                      _formatNumber(dueTodayAmount),
                      Icons.schedule,
                      Colors.amber,
                      'د.ع',
                      subtitle: '${dueTodayDebts.length} كارت',
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // قسم أنواع الكروت
              if (sortedCardTypes.isNotEmpty) ...[
                const Text(
                  'أنواع الكروت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const SizedBox(height: 12),

                // عرض أنواع الكروت في شبكة
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 12,
                    mainAxisSpacing: 12,
                    childAspectRatio: 2.5,
                  ),
                  itemCount: sortedCardTypes.length,
                  itemBuilder: (context, index) {
                    final cardType = sortedCardTypes[index];
                    final colors = [
                      Colors.orange,
                      Colors.blue,
                      Colors.green,
                      Colors.purple,
                      Colors.red,
                      Colors.teal,
                    ];
                    final color = colors[index % colors.length];

                    return _buildStatCard(
                      cardType.key,
                      '${cardType.value}',
                      Icons.credit_card,
                      color,
                      'كارت',
                      subtitle:
                          '${((cardType.value / customerDebts.length) * 100).toStringAsFixed(1)}%',
                    );
                  },
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    MaterialColor color,
    String unit, {
    String? subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.white, color.shade50.withValues(alpha: 0.3)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.shade200),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.white.withValues(alpha: 0.7),
            blurRadius: 8,
            offset: const Offset(-3, -3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and Title Row
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [color.shade400, color.shade600],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Icon(icon, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Value
          Row(
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                value,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color.shade700,
                ),
              ),
              const SizedBox(width: 4),
              Text(
                unit,
                style: TextStyle(
                  fontSize: 12,
                  color: color.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                subtitle,
                style: TextStyle(
                  fontSize: 11,
                  color: color.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  // دالة تنسيق الأرقام مع فاصل الآلاف
  String _formatNumber(double number) {
    final formatter = NumberFormat('#,###', 'en_US');
    return formatter.format(number.round());
  }
}
