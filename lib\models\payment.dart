enum PaymentType {
  full,
  partial,
}

class Payment {
  Payment({
    this.id,
    required this.debtId,
    required this.customerId,
    required this.amount,
    required this.type,
    this.notes,
    required this.paymentDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Payment.fromMap(Map<String, dynamic> map) {
    return Payment(
      id: map['id']?.toInt(),
      debtId: map['debt_id']?.toInt() ?? 0,
      customerId: map['customer_id']?.toInt() ?? 0,
      amount: map['amount']?.toDouble() ?? 0.0,
      type: PaymentType.values[map['type'] ?? 0],
      notes: map['notes'],
      paymentDate: DateTime.fromMillisecondsSinceEpoch(map['payment_date']),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at']),
    );
  }

  final int? id;
  final int debtId;
  final int customerId;
  final double amount;
  final PaymentType type;
  final String? notes;
  final DateTime paymentDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'debt_id': debtId,
      'customer_id': customerId,
      'amount': amount,
      'type': type.index,
      'notes': notes,
      'payment_date': paymentDate.millisecondsSinceEpoch,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
    };
  }

  Payment copyWith({
    int? id,
    int? debtId,
    int? customerId,
    double? amount,
    PaymentType? type,
    String? notes,
    DateTime? paymentDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Payment(
      id: id ?? this.id,
      debtId: debtId ?? this.debtId,
      customerId: customerId ?? this.customerId,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      notes: notes ?? this.notes,
      paymentDate: paymentDate ?? this.paymentDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Payment(id: $id, debtId: $debtId, customerId: $customerId, amount: $amount, type: $type, notes: $notes, paymentDate: $paymentDate, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
  
    return other is Payment &&
      other.id == id &&
      other.debtId == debtId &&
      other.customerId == customerId &&
      other.amount == amount &&
      other.type == type &&
      other.notes == notes &&
      other.paymentDate == paymentDate &&
      other.createdAt == createdAt &&
      other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
      debtId.hashCode ^
      customerId.hashCode ^
      amount.hashCode ^
      type.hashCode ^
      notes.hashCode ^
      paymentDate.hashCode ^
      createdAt.hashCode ^
      updatedAt.hashCode;
  }
}

extension PaymentTypeExtension on PaymentType {
  String get displayName {
    switch (this) {
      case PaymentType.full:
        return 'تسديد كامل';
      case PaymentType.partial:
        return 'تسديد جزئي';
    }
  }
}
