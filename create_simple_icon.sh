#!/bin/bash

# Create a simple app icon using ImageMagick (if available)
# This script creates a basic icon with text

# Check if ImageMagick is available
if command -v convert &> /dev/null; then
    echo "Creating app icon with ImageMagick..."
    
    # Create main icon (512x512)
    convert -size 512x512 xc:"#2a5298" \
        -fill white -stroke white -strokewidth 8 \
        -draw "circle 256,256 256,50" \
        -fill white -pointsize 48 -gravity center \
        -annotate +0-50 "محاسب ديون" \
        -fill white -pointsize 32 -gravity center \
        -annotate +0+20 "احترافي" \
        -fill "#FFD700" -stroke white -strokewidth 2 \
        -draw "circle 400,150 400,130" \
        -fill "#4CAF50" \
        -draw "rectangle 320,180 380,210" \
        assets/icons/app_icon.png
    
    echo "App icon created successfully!"
    
    # Create smaller versions
    for size in 192 144 96 72 48 36; do
        convert assets/icons/app_icon.png -resize ${size}x${size} assets/icons/app_icon_${size}.png
        echo "Created ${size}x${size} icon"
    done
    
else
    echo "ImageMagick not found. Creating a simple placeholder icon..."
    
    # Create a simple SVG icon
    cat > assets/icons/app_icon.svg << 'EOF'
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <circle cx="256" cy="256" r="240" fill="#2a5298" stroke="#ffffff" stroke-width="8"/>
  <rect x="180" y="150" width="152" height="200" rx="12" fill="#ffffff"/>
  <rect x="195" y="170" width="122" height="40" rx="6" fill="#2c3e50"/>
  <text x="256" y="195" text-anchor="middle" fill="#00ff88" font-size="20" font-family="Arial">123,456</text>
  <circle cx="210" cy="240" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="210" y="245" text-anchor="middle" fill="#495057" font-size="10">7</text>
  <circle cx="256" cy="240" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="256" y="245" text-anchor="middle" fill="#495057" font-size="10">8</text>
  <circle cx="302" cy="240" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="302" y="245" text-anchor="middle" fill="#495057" font-size="10">9</text>
  <circle cx="210" cy="270" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="210" y="275" text-anchor="middle" fill="#495057" font-size="10">4</text>
  <circle cx="256" cy="270" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="256" y="275" text-anchor="middle" fill="#495057" font-size="10">5</text>
  <circle cx="302" cy="270" r="12" fill="#f8f9fa" stroke="#dee2e6"/>
  <text x="302" y="275" text-anchor="middle" fill="#495057" font-size="10">6</text>
  <rect x="340" y="170" width="40" height="25" rx="4" fill="#4CAF50"/>
  <circle cx="380" y="220" r="12" fill="#FFD700"/>
  <text x="256" y="400" text-anchor="middle" fill="#ffffff" font-size="32" font-family="Arial">محاسب ديون</text>
  <text x="256" y="430" text-anchor="middle" fill="#ffffff" font-size="20" font-family="Arial">احترافي</text>
</svg>
EOF
    
    echo "SVG icon created. You can convert it to PNG using online tools."
fi
