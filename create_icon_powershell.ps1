# PowerShell script to create a professional app icon
Add-Type -AssemblyName System.Drawing

# Create a 512x512 bitmap
$size = 512
$bmp = New-Object System.Drawing.Bitmap($size, $size)
$graphics = [System.Drawing.Graphics]::FromImage($bmp)
$graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::AntiAlias

# Background gradient
$center = $size / 2
$radius = 240

# Create gradient brush
$gradientBrush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
    [System.Drawing.Point]::new(0, 0),
    [System.Drawing.Point]::new($size, $size),
    [System.Drawing.Color]::FromArgb(30, 60, 114),
    [System.Drawing.Color]::FromArgb(61, 108, 185)
)

# Draw background circle
$graphics.FillEllipse($gradientBrush, $center - $radius, $center - $radius, $radius * 2, $radius * 2)

# White border
$whitePen = New-Object System.Drawing.Pen([System.Drawing.Color]::White, 8)
$graphics.DrawEllipse($whitePen, $center - $radius, $center - $radius, $radius * 2, $radius * 2)

# Calculator body
$calcWidth = 200
$calcHeight = 250
$calcX = $center - $calcWidth / 2
$calcY = $center - $calcHeight / 2

# Calculator shadow
$shadowBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(80, 0, 0, 0))
$graphics.FillRoundedRectangle($shadowBrush, $calcX + 5, $calcY + 5, $calcWidth, $calcHeight, 12)

# Calculator body
$whiteBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::White)
$graphics.FillRoundedRectangle($whiteBrush, $calcX, $calcY, $calcWidth, $calcHeight, 12)

$grayPen = New-Object System.Drawing.Pen([System.Drawing.Color]::LightGray, 2)
$graphics.DrawRoundedRectangle($grayPen, $calcX, $calcY, $calcWidth, $calcHeight, 12)

# Display screen
$displayWidth = 180
$displayHeight = 50
$displayX = $calcX + 10
$displayY = $calcY + 15

$displayBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(44, 62, 80))
$graphics.FillRoundedRectangle($displayBrush, $displayX, $displayY, $displayWidth, $displayHeight, 6)

# Display text
$font = New-Object System.Drawing.Font("Arial", 18, [System.Drawing.FontStyle]::Bold)
$greenBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 255, 136))
$graphics.DrawString("123,456", $font, $greenBrush, $displayX + $displayWidth - 80, $displayY + 15)

# Currency
$smallFont = New-Object System.Drawing.Font("Arial", 10)
$lightBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(200, 255, 255, 255))
$graphics.DrawString("د.ع", $smallFont, $lightBrush, $displayX + 5, $displayY + 5)

# Calculator buttons
$buttonSize = 25
$buttonSpacing = 35
$startX = $calcX + 20
$startY = $calcY + 80

$buttons = @(
    @("C", "÷", "×", "⌫"),
    @("7", "8", "9", "-"),
    @("4", "5", "6", "+"),
    @("1", "2", "3", "=")
)

$buttonFont = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)

for ($row = 0; $row -lt $buttons.Length; $row++) {
    for ($col = 0; $col -lt $buttons[$row].Length; $col++) {
        $x = $startX + $col * $buttonSpacing
        $y = $startY + $row * $buttonSpacing
        $btnText = $buttons[$row][$col]
        
        # Button color
        if ($btnText -in @("C", "÷", "×", "⌫", "-", "+")) {
            $btnBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(0, 123, 255))
            $textBrush = $whiteBrush
        } elseif ($btnText -eq "=") {
            $btnBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(40, 167, 69))
            $textBrush = $whiteBrush
        } else {
            $btnBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(248, 249, 250))
            $textBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(73, 80, 87))
        }
        
        # Draw button
        $graphics.FillEllipse($btnBrush, $x - 12, $y - 12, 24, 24)
        $graphics.DrawEllipse($grayPen, $x - 12, $y - 12, 24, 24)
        
        # Button text
        $textSize = $graphics.MeasureString($btnText, $buttonFont)
        $textX = $x - $textSize.Width / 2
        $textY = $y - $textSize.Height / 2
        $graphics.DrawString($btnText, $buttonFont, $textBrush, $textX, $textY)
    }
}

# 0 button (wider)
$zeroX = $startX
$zeroY = $startY + 4 * $buttonSpacing
$zeroBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(248, 249, 250))
$graphics.FillRoundedRectangle($zeroBrush, $zeroX - 12, $zeroY - 12, 60, 24, 12)
$graphics.DrawRoundedRectangle($grayPen, $zeroX - 12, $zeroY - 12, 60, 24, 12)

$textSize = $graphics.MeasureString("0", $buttonFont)
$textX = $zeroX + 18 - $textSize.Width / 2
$textY = $zeroY - $textSize.Height / 2
$darkBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(73, 80, 87))
$graphics.DrawString("0", $buttonFont, $darkBrush, $textX, $textY)

# . button
$dotX = $startX + 2 * $buttonSpacing
$dotY = $zeroY
$graphics.FillEllipse($zeroBrush, $dotX - 12, $dotY - 12, 24, 24)
$graphics.DrawEllipse($grayPen, $dotX - 12, $dotY - 12, 24, 24)

$textSize = $graphics.MeasureString(".", $buttonFont)
$textX = $dotX - $textSize.Width / 2
$textY = $dotY - $textSize.Height / 2
$graphics.DrawString(".", $buttonFont, $darkBrush, $textX, $textY)

# Credit card
$cardX = $calcX + $calcWidth + 20
$cardY = $calcY + 30
$cardWidth = 60
$cardHeight = 38

$cardBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(76, 175, 80))
$graphics.FillRoundedRectangle($cardBrush, $cardX, $cardY, $cardWidth, $cardHeight, 6)
$graphics.DrawRoundedRectangle($whitePen, $cardX, $cardY, $cardWidth, $cardHeight, 6)

# Card stripe
$graphics.FillRectangle($lightBrush, $cardX + 5, $cardY + 10, $cardWidth - 10, 6)

# Coins
$coin1X = $cardX + 10
$coin1Y = $cardY + 60
$coin2X = $cardX + 35
$coin2Y = $cardY + 80

$goldBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(255, 215, 0))

# Coin 1
$graphics.FillEllipse($goldBrush, $coin1X - 15, $coin1Y - 15, 30, 30)
$graphics.DrawEllipse($whitePen, $coin1X - 15, $coin1Y - 15, 30, 30)
$coinFont = New-Object System.Drawing.Font("Arial", 8, [System.Drawing.FontStyle]::Bold)
$brownBrush = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(139, 69, 19))
$textSize = $graphics.MeasureString("د.ع", $coinFont)
$graphics.DrawString("د.ع", $coinFont, $brownBrush, $coin1X - $textSize.Width / 2, $coin1Y - $textSize.Height / 2)

# Coin 2
$graphics.FillEllipse($goldBrush, $coin2X - 12, $coin2Y - 12, 24, 24)
$graphics.DrawEllipse($whitePen, $coin2X - 12, $coin2Y - 12, 24, 24)
$graphics.DrawString("د.ع", $coinFont, $brownBrush, $coin2X - $textSize.Width / 2, $coin2Y - $textSize.Height / 2)

# Title
$titleFont = New-Object System.Drawing.Font("Arial", 24, [System.Drawing.FontStyle]::Bold)
$titleText = "محاسب ديون احترافي"
$textSize = $graphics.MeasureString($titleText, $titleFont)
$titleX = $center - $textSize.Width / 2
$titleY = $center + 180

# Title shadow
$shadowBrush2 = New-Object System.Drawing.SolidBrush([System.Drawing.Color]::FromArgb(100, 0, 0, 0))
$graphics.DrawString($titleText, $titleFont, $shadowBrush2, $titleX + 2, $titleY + 2)

# Title text
$graphics.DrawString($titleText, $titleFont, $whiteBrush, $titleX, $titleY)

# Save the image
$bmp.Save("assets\icons\app_icon.png", [System.Drawing.Imaging.ImageFormat]::Png)

# Create different sizes
$sizes = @(192, 144, 96, 72, 48, 36)
foreach ($s in $sizes) {
    $resized = New-Object System.Drawing.Bitmap($s, $s)
    $resizedGraphics = [System.Drawing.Graphics]::FromImage($resized)
    $resizedGraphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
    $resizedGraphics.DrawImage($bmp, 0, 0, $s, $s)
    $resized.Save("assets\icons\app_icon_$s.png", [System.Drawing.Imaging.ImageFormat]::Png)
    $resizedGraphics.Dispose()
    $resized.Dispose()
    Write-Host "✅ Created ${s}x${s} icon"
}

# Cleanup
$graphics.Dispose()
$bmp.Dispose()
$gradientBrush.Dispose()
$whitePen.Dispose()
$shadowBrush.Dispose()
$whiteBrush.Dispose()
$grayPen.Dispose()

Write-Host "✅ Professional app icon created successfully!"
