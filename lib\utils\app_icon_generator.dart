import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

class AppIconGenerator extends StatelessWidget {
  const AppIconGenerator({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مولد أيقونة التطبيق'),
        backgroundColor: const Color(0xFF2a5298),
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            RepaintBoundary(
              key: const GlobalObjectKey('app_icon'),
              child: const AppIconWidget(),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => _generateIcon(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2a5298),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
              ),
              child: const Text('إنشاء الأيقونة'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateIcon(BuildContext context) async {
    try {
      RenderRepaintBoundary boundary = context
          .findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      
      if (byteData != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء الأيقونة بنجاح!')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إنشاء الأيقونة: $e')),
      );
    }
  }
}

class AppIconWidget extends StatelessWidget {
  const AppIconWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: 300,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1e3c72),
            Color(0xFF2a5298),
          ],
        ),
        border: Border.all(color: Colors.white, width: 6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Calculator body
          Container(
            width: 140,
            height: 180,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Display
                Container(
                  margin: const EdgeInsets.all(10),
                  height: 35,
                  decoration: BoxDecoration(
                    color: const Color(0xFF2c3e50),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Center(
                    child: Text(
                      '123,456',
                      style: TextStyle(
                        color: Color(0xFF00ff88),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                // Buttons
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: GridView.count(
                      crossAxisCount: 4,
                      mainAxisSpacing: 4,
                      crossAxisSpacing: 4,
                      children: [
                        _buildButton('7'),
                        _buildButton('8'),
                        _buildButton('9'),
                        _buildButton('÷', isOperator: true),
                        _buildButton('4'),
                        _buildButton('5'),
                        _buildButton('6'),
                        _buildButton('×', isOperator: true),
                        _buildButton('1'),
                        _buildButton('2'),
                        _buildButton('3'),
                        _buildButton('=', isEquals: true),
                        _buildButton('0'),
                        _buildButton('.'),
                        _buildButton('+', isOperator: true),
                        _buildButton('C', isOperator: true),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Credit card
          Positioned(
            top: 60,
            right: 40,
            child: Container(
              width: 40,
              height: 25,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF4CAF50), Color(0xFF45a049)],
                ),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(color: Colors.white, width: 1),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    width: 30,
                    height: 4,
                    color: Colors.white.withOpacity(0.8),
                  ),
                  const SizedBox(height: 2),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(4, (index) => Container(
                      width: 2,
                      height: 2,
                      margin: const EdgeInsets.symmetric(horizontal: 1),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                    )),
                  ),
                ],
              ),
            ),
          ),
          
          // Coins
          Positioned(
            bottom: 80,
            right: 30,
            child: Column(
              children: [
                _buildCoin(20),
                const SizedBox(height: 4),
                _buildCoin(16),
              ],
            ),
          ),
          
          // Title
          const Positioned(
            bottom: 30,
            child: Column(
              children: [
                Text(
                  'محاسب ديون',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'احترافي',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton(String text, {bool isOperator = false, bool isEquals = false}) {
    Color backgroundColor;
    Color textColor;
    
    if (isEquals) {
      backgroundColor = const Color(0xFF28a745);
      textColor = Colors.white;
    } else if (isOperator) {
      backgroundColor = const Color(0xFF007bff);
      textColor = Colors.white;
    } else {
      backgroundColor = const Color(0xFFf8f9fa);
      textColor = const Color(0xFF495057);
    }
    
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        shape: BoxShape.circle,
        border: Border.all(color: const Color(0xFFdee2e6), width: 0.5),
      ),
      child: Center(
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildCoin(double size) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
        ),
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 1),
      ),
      child: Center(
        child: Text(
          'د.ع',
          style: TextStyle(
            color: const Color(0xFF8B4513),
            fontSize: size * 0.3,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}
