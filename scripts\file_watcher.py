#!/usr/bin/env python3
"""
Flutter File Watcher - Monitors file changes and auto-reloads
"""

import os
import time
import subprocess
import sys
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class FlutterReloadHandler(FileSystemEventHandler):
    def __init__(self):
        self.last_reload = 0
        self.reload_delay = 2  # seconds
        
    def on_modified(self, event):
        if event.is_directory:
            return
            
        # Only watch Dart files
        if not event.src_path.endswith('.dart'):
            return
            
        current_time = time.time()
        if current_time - self.last_reload < self.reload_delay:
            return
            
        print(f"File changed: {event.src_path}")
        self.trigger_hot_reload()
        self.last_reload = current_time
        
    def trigger_hot_reload(self):
        try:
            # Try to send 'r' to flutter process
            result = subprocess.run(['flutter', 'attach'], 
                                  input='r\n', 
                                  text=True, 
                                  capture_output=True, 
                                  timeout=5)
            print("Hot reload triggered")
        except Exception as e:
            print(f"Failed to trigger hot reload: {e}")

def main():
    print("🔥 Flutter File Watcher Started")
    print("Monitoring lib/ directory for changes...")
    
    # Watch the lib directory
    lib_path = Path("lib")
    if not lib_path.exists():
        print("❌ lib/ directory not found!")
        sys.exit(1)
        
    event_handler = FlutterReloadHandler()
    observer = Observer()
    observer.schedule(event_handler, str(lib_path), recursive=True)
    
    observer.start()
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        observer.stop()
        print("\n🛑 File watcher stopped")
        
    observer.join()

if __name__ == "__main__":
    main()
