<!DOCTYPE html>
<html>
<head>
    <title>App Icon Generator</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 20px; }
        canvas { border: 2px solid #ccc; margin: 20px; }
        button { padding: 10px 20px; font-size: 16px; margin: 10px; }
    </style>
</head>
<body>
    <h1>محاسب ديون احترافي - مولد الأيقونة</h1>
    <canvas id="iconCanvas" width="512" height="512"></canvas>
    <br>
    <button onclick="generateIcon()">إنشاء الأيقونة</button>
    <button onclick="downloadIcon()">تحميل الأيقونة</button>
    
    <script>
        function generateIcon() {
            const canvas = document.getElementById('iconCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, 512, 512);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, 512, 512);
            gradient.addColorStop(0, '#1e3c72');
            gradient.addColorStop(1, '#2a5298');
            
            // Draw background circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(256, 256, 240, 0, 2 * Math.PI);
            ctx.fill();
            
            // White border
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 8;
            ctx.stroke();
            
            // Calculator body
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(140, 120, 232, 280);
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 2;
            ctx.strokeRect(140, 120, 232, 280);
            
            // Display screen
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(160, 140, 192, 60);
            
            // Display text
            ctx.fillStyle = '#00ff88';
            ctx.font = 'bold 24px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('123,456', 256, 175);
            
            // Calculator buttons
            const buttons = [
                {x: 180, y: 240, text: '7'},
                {x: 220, y: 240, text: '8'},
                {x: 260, y: 240, text: '9'},
                {x: 300, y: 240, text: '÷', color: '#007bff'},
                {x: 180, y: 280, text: '4'},
                {x: 220, y: 280, text: '5'},
                {x: 260, y: 280, text: '6'},
                {x: 300, y: 280, text: '×', color: '#007bff'},
                {x: 180, y: 320, text: '1'},
                {x: 220, y: 320, text: '2'},
                {x: 260, y: 320, text: '3'},
                {x: 300, y: 320, text: '=', color: '#28a745'},
                {x: 200, y: 360, text: '0'},
                {x: 260, y: 360, text: '.'}
            ];
            
            buttons.forEach(btn => {
                ctx.fillStyle = btn.color || '#f8f9fa';
                ctx.beginPath();
                ctx.arc(btn.x, btn.y, 18, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.strokeStyle = '#dee2e6';
                ctx.lineWidth = 1;
                ctx.stroke();
                
                ctx.fillStyle = btn.color ? '#ffffff' : '#495057';
                ctx.font = 'bold 14px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(btn.text, btn.x, btn.y + 5);
            });
            
            // Credit card
            ctx.fillStyle = '#4CAF50';
            ctx.fillRect(320, 140, 60, 38);
            ctx.strokeStyle = '#ffffff';
            ctx.lineWidth = 2;
            ctx.strokeRect(320, 140, 60, 38);
            
            // Card stripe
            ctx.fillStyle = 'rgba(255,255,255,0.8)';
            ctx.fillRect(325, 150, 50, 8);
            
            // Coins
            const coins = [{x: 380, y: 200, r: 16}, {x: 400, y: 220, r: 14}];
            coins.forEach(coin => {
                const coinGradient = ctx.createLinearGradient(
                    coin.x - coin.r, coin.y - coin.r,
                    coin.x + coin.r, coin.y + coin.r
                );
                coinGradient.addColorStop(0, '#FFD700');
                coinGradient.addColorStop(1, '#FFA500');
                
                ctx.fillStyle = coinGradient;
                ctx.beginPath();
                ctx.arc(coin.x, coin.y, coin.r, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                ctx.stroke();
                
                ctx.fillStyle = '#8B4513';
                ctx.font = `bold ${coin.r * 0.6}px Arial`;
                ctx.textAlign = 'center';
                ctx.fillText('د.ع', coin.x, coin.y + 4);
            });
            
            // Title
            ctx.fillStyle = '#ffffff';
            ctx.font = 'bold 28px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('محاسب ديون', 256, 450);
            
            ctx.font = '18px Arial';
            ctx.fillText('احترافي', 256, 480);
        }
        
        function downloadIcon() {
            const canvas = document.getElementById('iconCanvas');
            const link = document.createElement('a');
            link.download = 'app_icon.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // Generate icon on page load
        window.onload = generateIcon;
    </script>
</body>
</html>
