import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../models/customer.dart';
import '../models/debt.dart';
import '../models/custom_card_type.dart';
import '../providers/customer_provider.dart';
import '../providers/debt_provider.dart';
import '../providers/card_type_provider.dart';
import '../utils/number_formatter.dart';

class CustomersOverviewScreen extends StatefulWidget {
  const CustomersOverviewScreen({super.key});

  @override
  State<CustomersOverviewScreen> createState() =>
      _CustomersOverviewScreenState();
}

class _CustomersOverviewScreenState extends State<CustomersOverviewScreen> {
  List<CustomerOverviewData> _overviewData = [];
  List<CustomerOverviewData> _filteredData = [];
  bool _isLoading = true;
  String _sortColumn = 'name';
  bool _sortAscending = true;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadOverviewData();
    _searchController.addListener(_filterData);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadOverviewData() async {
    setState(() => _isLoading = true);

    final customerProvider = Provider.of<CustomerProvider>(
      context,
      listen: false,
    );
    final debtProvider = Provider.of<DebtProvider>(context, listen: false);
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    await customerProvider.loadCustomers();
    await debtProvider.loadAllDebts();
    await cardTypeProvider.loadCustomCardTypes();

    final customers = customerProvider.customers;
    final allDebts = debtProvider.debts;

    _overviewData = customers.map((customer) {
      final customerDebts = allDebts
          .where((debt) => debt.customerId == customer.id)
          .toList();

      return CustomerOverviewData(customer: customer, debts: customerDebts);
    }).toList();

    _sortData();
    _filterData();
    setState(() => _isLoading = false);
  }

  void _filterData() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredData = List.from(_overviewData);
      } else {
        _filteredData = _overviewData.where((data) {
          return data.customer.name.toLowerCase().contains(query) ||
              (data.customer.phone?.toLowerCase().contains(query) ?? false);
        }).toList();
      }
    });
  }

  void _sortData() {
    _filteredData.sort((a, b) {
      dynamic aValue, bValue;

      switch (_sortColumn) {
        case 'name':
          aValue = a.customer.name;
          bValue = b.customer.name;
          break;
        case 'quantity':
          aValue = a.debts.isNotEmpty ? a.debts.last.quantity : 0;
          bValue = b.debts.isNotEmpty ? b.debts.last.quantity : 0;
          break;
        case 'cardType':
          aValue = a.debts.isNotEmpty ? a.debts.last.cardType : '';
          bValue = b.debts.isNotEmpty ? b.debts.last.cardType : '';
          break;
        case 'price':
          aValue = a.debts.isNotEmpty ? a.debts.last.amount : 0;
          bValue = b.debts.isNotEmpty ? b.debts.last.amount : 0;
          break;
        case 'createdDate':
          aValue = a.lastDebtDate?.millisecondsSinceEpoch ?? 0;
          bValue = b.lastDebtDate?.millisecondsSinceEpoch ?? 0;
          break;
        case 'notes':
          aValue = a.debts.isNotEmpty ? (a.debts.last.notes ?? '') : '';
          bValue = b.debts.isNotEmpty ? (b.debts.last.notes ?? '') : '';
          break;
        case 'dueDate':
          aValue = a.debts.isNotEmpty
              ? a.debts.last.dueDate.millisecondsSinceEpoch
              : 0;
          bValue = b.debts.isNotEmpty
              ? b.debts.last.dueDate.millisecondsSinceEpoch
              : 0;
          break;
        case 'daysSinceCreated':
          aValue = a.debts.isNotEmpty
              ? _calculateDaysSince(a.debts.last.createdAt)
              : 0;
          bValue = b.debts.isNotEmpty
              ? _calculateDaysSince(b.debts.last.createdAt)
              : 0;
          break;
        case 'daysUntilDue':
          aValue = a.debts.isNotEmpty
              ? DateTime.now().difference(a.debts.last.dueDate).inDays
              : 0;
          bValue = b.debts.isNotEmpty
              ? DateTime.now().difference(b.debts.last.dueDate).inDays
              : 0;
          break;
        default:
          aValue = a.customer.name;
          bValue = b.customer.name;
      }

      if (aValue is String && bValue is String) {
        return _sortAscending
            ? aValue.compareTo(bValue)
            : bValue.compareTo(aValue);
      } else {
        return _sortAscending
            ? aValue.compareTo(bValue)
            : bValue.compareTo(aValue);
      }
    });
  }

  void _onSort(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
      _sortData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text(
          'نظرة عامة على العملاء',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadOverviewData,
            tooltip: 'تحديث',
          ),
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: _exportToExcel,
            tooltip: 'تصدير إلى Excel',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildSearchBar(),
                Expanded(child: _buildDataTable()),
              ],
            ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث عن عميل...',
          prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: Colors.grey.shade600),
                  onPressed: () {
                    _searchController.clear();
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: const TextStyle(fontSize: 14),
      ),
    );
  }

  Widget _buildDataTable() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          sortColumnIndex: _getSortColumnIndex(),
          sortAscending: _sortAscending,
          headingRowColor: WidgetStateProperty.all(Colors.grey.shade100),
          headingTextStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.black87,
            fontSize: 12,
          ),
          dataTextStyle: const TextStyle(fontSize: 11, color: Colors.black87),
          columnSpacing: 20,
          horizontalMargin: 12,
          columns: _buildColumns(),
          rows: _buildRows(),
        ),
      ),
    );
  }

  List<DataColumn> _buildColumns() {
    return [
      _buildDataColumn('الرقم', 'index', 50),
      _buildDataColumn('اسم العميل', 'name', 120),
      _buildDataColumn('الكمية', 'quantity', 70),
      _buildDataColumn('نوع الكارت', 'cardType', 100),
      _buildDataColumn('السعر', 'price', 80),
      _buildDataColumn('تاريخ القيد', 'createdDate', 120),
      _buildDataColumn('الملاحظات', 'notes', 100),
      _buildDataColumn('تاريخ الاستحقاق', 'dueDate', 120),
      _buildDataColumn('منذ البيع', 'daysSinceCreated', 80),
      _buildDataColumn('متبقي للموعد', 'daysUntilDue', 90),
    ];
  }

  DataColumn _buildDataColumn(String label, String key, double width) {
    return DataColumn(
      label: SizedBox(
        width: width,
        child: InkWell(
          onTap: () => _onSort(key),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: Text(
                  label,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              if (_sortColumn == key)
                Icon(
                  _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                  size: 16,
                  color: Colors.blue.shade600,
                ),
            ],
          ),
        ),
      ),
    );
  }

  List<DataRow> _buildRows() {
    return _filteredData.asMap().entries.map((entry) {
      final index = entry.key + 1;
      final data = entry.value;

      // تحديد لون الصف حسب تاريخ آخر دين
      final lastDebt = data.debts.isNotEmpty ? data.debts.last : null;
      Color? rowColor;

      if (lastDebt != null) {
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final yesterday = today.subtract(const Duration(days: 1));
        final debtDate = DateTime(
          lastDebt.createdAt.year,
          lastDebt.createdAt.month,
          lastDebt.createdAt.day,
        );

        final dueDate = DateTime(
          lastDebt.dueDate.year,
          lastDebt.dueDate.month,
          lastDebt.dueDate.day,
        );

        // أولوية للديون المتأخرة أو المنتهية - أحمر طوخ
        if ((dueDate.isBefore(today) || dueDate == today) &&
            lastDebt.paidAmount < lastDebt.amount) {
          rowColor = Colors.red.shade200; // أحمر طوخ للمتأخر أو المنتهي اليوم
        } else if (debtDate == today) {
          rowColor = Colors.green.shade50; // بيع اليوم - أخضر فاتح
        } else if (debtDate == yesterday) {
          rowColor = Colors.orange.shade50; // بيع الأمس - برتقالي
        }
      }

      return DataRow(
        color: WidgetStateProperty.resolveWith<Color?>((states) {
          return rowColor ??
              (index % 2 == 0 ? Colors.grey.shade50 : Colors.white);
        }),
        cells: [
          DataCell(Text(index.toString())),
          // اسم العميل
          DataCell(
            SizedBox(
              width: 120,
              child: Text(
                data.customer.name,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ),
          // الكمية
          DataCell(
            Text(
              lastDebt?.quantity.toString() ?? '-',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          // نوع الكارت
          DataCell(
            SizedBox(
              width: 100,
              child: Text(
                lastDebt?.cardType != null
                    ? _getCardTypeDisplayName(lastDebt!.cardType)
                    : '-',
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 12),
              ),
            ),
          ),
          // السعر
          DataCell(
            Text(
              lastDebt != null
                  ? NumberFormatter.formatCurrency(lastDebt.amount.toInt())
                  : '-',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          // تاريخ القيد مع اسم اليوم والوقت
          DataCell(
            SizedBox(
              width: 120,
              child: Text(
                lastDebt != null ? _formatDateWithDay(lastDebt.createdAt) : '-',
                style: const TextStyle(fontSize: 10),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          // الملاحظات
          DataCell(
            SizedBox(
              width: 100,
              child: Text(
                lastDebt?.notes?.isNotEmpty == true ? lastDebt!.notes! : '-',
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 10),
              ),
            ),
          ),
          // تاريخ الاستحقاق مع اسم اليوم
          DataCell(
            SizedBox(
              width: 120,
              child: Text(
                lastDebt != null
                    ? _formatDueDateWithDay(lastDebt.dueDate)
                    : '-',
                style: const TextStyle(fontSize: 10),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          // عداد من البيع
          DataCell(
            Text(
              lastDebt != null
                  ? '${_calculateDaysSince(lastDebt.createdAt)} يوم'
                  : '-',
              style: const TextStyle(fontSize: 10),
            ),
          ),
          // عداد كم متبقي للموعد
          DataCell(
            Text(
              lastDebt != null ? _formatDaysUntilDue(lastDebt.dueDate) : '-',
              style: TextStyle(
                fontSize: 10,
                color:
                    lastDebt != null &&
                        lastDebt.dueDate.isBefore(DateTime.now())
                    ? Colors.red.shade700
                    : Colors.black87,
              ),
            ),
          ),
        ],
      );
    }).toList();
  }

  int? _getSortColumnIndex() {
    switch (_sortColumn) {
      case 'name':
        return 1;
      case 'quantity':
        return 2;
      case 'cardType':
        return 3;
      case 'price':
        return 4;
      case 'createdDate':
        return 5;
      case 'notes':
        return 6;
      case 'dueDate':
        return 7;
      case 'daysSinceCreated':
        return 8;
      case 'daysUntilDue':
        return 9;
      default:
        return null;
    }
  }

  // تنسيق التاريخ مع اسم اليوم والوقت (للقيد)
  String _formatDateWithDay(DateTime dateTime) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final dayName = dayNames[dateTime.weekday % 7];
    final hour = dateTime.hour;
    final minute = dateTime.minute;
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final timeFormat =
        '$displayHour:${minute.toString().padLeft(2, '0')} $period';
    final dateFormat = DateFormat('yyyy/M/d').format(dateTime);
    return '$dateFormat - $dayName $timeFormat';
  }

  // تنسيق تاريخ الاستحقاق (بدون وقت)
  String _formatDueDateWithDay(DateTime dateTime) {
    final dayNames = [
      'الأحد',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    final dayName = dayNames[dateTime.weekday % 7];
    final dateFormat = DateFormat('yyyy/M/d').format(dateTime);
    return '$dateFormat - $dayName';
  }

  // حساب عدد الأيام منذ تاريخ معين
  int _calculateDaysSince(DateTime fromDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final compareDate = DateTime(fromDate.year, fromDate.month, fromDate.day);
    return today.difference(compareDate).inDays;
  }

  // تنسيق عدد الأيام المتبقية للموعد
  String _formatDaysUntilDue(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final compareDate = DateTime(dueDate.year, dueDate.month, dueDate.day);
    final difference = compareDate.difference(today).inDays;

    if (difference < 0) {
      return 'متأخر ${-difference} يوم';
    } else if (difference == 0) {
      return 'مستحق اليوم';
    } else {
      return 'باقي $difference يوم';
    }
  }

  // الحصول على اسم عرض نوع البطاقة بالعربية
  String _getCardTypeDisplayName(String cardType) {
    // استخدام CardTypeProvider للحصول على الاسم الصحيح
    final cardTypeProvider = Provider.of<CardTypeProvider>(
      context,
      listen: false,
    );

    // أولاً، نحاول البحث بالمعرف المباشر
    CardTypeOption? cardTypeOption = cardTypeProvider.getCardTypeById(cardType);

    // إذا لم نجد، نحاول البحث في الأنواع الافتراضية بطرق مختلفة
    cardTypeOption ??= cardTypeProvider.allCardTypes
        .cast<CardTypeOption?>()
        .firstWhere(
          (type) =>
              type != null &&
              (type.id.toLowerCase() == cardType.toLowerCase() ||
                  type.defaultType?.name.toLowerCase() ==
                      cardType.toLowerCase() ||
                  (type.defaultType != null &&
                      _compareCardTypes(type.defaultType!.name, cardType))),
          orElse: () => null,
        );

    if (cardTypeOption != null) {
      return cardTypeOption.displayName;
    }

    // إذا لم نجد النوع، نحاول التحويل اليدوي
    return _convertCardTypeToArabic(cardType);
  }

  // مقارنة أنواع البطاقات مع تجاهل الحالة والمسافات
  bool _compareCardTypes(String type1, String type2) {
    final clean1 = type1.toLowerCase().replaceAll(' ', '').replaceAll('_', '');
    final clean2 = type2.toLowerCase().replaceAll(' ', '').replaceAll('_', '');
    return clean1 == clean2;
  }

  // تحويل أسماء البطاقات إلى عربية
  String _convertCardTypeToArabic(String cardType) {
    final cleanType = cardType.trim().toLowerCase();

    switch (cleanType) {
      case 'cash':
        return 'نقدي';
      case 'visa':
        return 'فيزا';
      case 'mastercard':
        return 'ماستركارد';
      case 'americanexpress':
        return 'أمريكان إكسبريس';
      case 'zain':
        return 'زين';
      case 'sia':
      case 'asia': // إضافة دعم لـ asia أيضاً
        return 'آسيا';
      case 'abuashara':
        return 'أبو العشرة';
      case 'abusitta':
        return 'أبو الستة';
      case 'other':
        return 'أخرى';
      default:
        // إذا كان النص عربي، أعده كما هو
        if (_isArabicText(cardType)) {
          return cardType.isNotEmpty ? cardType : 'غير محدد';
        }
        // إذا كان إنجليزي ولم نجده، أعده كما هو
        return cardType.isNotEmpty ? cardType : 'غير محدد';
    }
  }

  // التحقق من كون النص عربي
  bool _isArabicText(String text) {
    if (text.isEmpty) return false;
    // التحقق من وجود أحرف عربية
    final arabicRegex = RegExp(r'[\u0600-\u06FF]');
    return arabicRegex.hasMatch(text);
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تنفيذ تصدير Excel قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}

class CustomerOverviewData {
  CustomerOverviewData({required this.customer, required this.debts});
  final Customer customer;
  final List<Debt> debts;

  DateTime? get lastDebtDate {
    if (debts.isEmpty) return null;
    return debts
        .map((debt) => debt.createdAt)
        .reduce((a, b) => a.isAfter(b) ? a : b);
  }
}
