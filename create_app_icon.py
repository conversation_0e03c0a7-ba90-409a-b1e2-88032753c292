#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a professional app icon for the debt accounting app
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_app_icon():
    # Create a 512x512 image with transparent background
    size = 512
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Background gradient circle
    center = size // 2
    radius = 240
    
    # Create gradient background
    for i in range(radius):
        alpha = int(255 * (1 - i / radius))
        color = (30, 60, 114, alpha)  # Blue gradient
        draw.ellipse([center - radius + i, center - radius + i, 
                     center + radius - i, center + radius - i], 
                    fill=color)
    
    # Main background circle
    draw.ellipse([center - radius, center - radius, 
                 center + radius, center + radius], 
                fill=(42, 82, 152, 255), outline=(255, 255, 255, 255), width=8)
    
    # Calculator body
    calc_x, calc_y = 140, 120
    calc_w, calc_h = 232, 280
    draw.rounded_rectangle([calc_x, calc_y, calc_x + calc_w, calc_y + calc_h], 
                          radius=16, fill=(255, 255, 255, 255), 
                          outline=(224, 224, 224, 255), width=2)
    
    # Display screen
    screen_x, screen_y = 160, 140
    screen_w, screen_h = 192, 60
    draw.rounded_rectangle([screen_x, screen_y, screen_x + screen_w, screen_y + screen_h], 
                          radius=8, fill=(44, 62, 80, 255))
    
    # Try to load a font, fallback to default if not available
    try:
        font_large = ImageFont.truetype("arial.ttf", 24)
        font_medium = ImageFont.truetype("arial.ttf", 18)
        font_small = ImageFont.truetype("arial.ttf", 14)
        font_title = ImageFont.truetype("arial.ttf", 32)
    except:
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
        font_title = ImageFont.load_default()
    
    # Display text
    draw.text((256, 170), "123,456", fill=(0, 255, 136, 255), 
             font=font_large, anchor="mm")
    
    # Calculator buttons
    buttons = [
        # Row 1
        (180, 240, "7"), (220, 240, "8"), (260, 240, "9"), (300, 240, "÷"),
        # Row 2  
        (180, 280, "4"), (220, 280, "5"), (260, 280, "6"), (300, 280, "×"),
        # Row 3
        (180, 320, "1"), (220, 320, "2"), (260, 320, "3"), (300, 320, "="),
        # Row 4
        (220, 360, "0"), (260, 360, ".")
    ]
    
    for x, y, text in buttons:
        if text in ["÷", "×", "="]:
            color = (0, 123, 255, 255) if text != "=" else (40, 167, 69, 255)
            text_color = (255, 255, 255, 255)
        else:
            color = (248, 249, 250, 255)
            text_color = (73, 80, 87, 255)
            
        draw.ellipse([x-18, y-18, x+18, y+18], fill=color, 
                    outline=(222, 226, 230, 255), width=1)
        draw.text((x, y), text, fill=text_color, font=font_small, anchor="mm")
    
    # Credit card
    card_x, card_y = 320, 140
    card_w, card_h = 60, 38
    draw.rounded_rectangle([card_x, card_y, card_x + card_w, card_y + card_h], 
                          radius=6, fill=(76, 175, 80, 255), 
                          outline=(255, 255, 255, 255), width=2)
    
    # Card stripe
    draw.rectangle([card_x + 5, card_y + 10, card_x + card_w - 5, card_y + 18], 
                  fill=(255, 255, 255, 200))
    
    # Card chip dots
    for i in range(4):
        draw.ellipse([card_x + 15 + i*10 - 3, card_y + 25 - 3, 
                     card_x + 15 + i*10 + 3, card_y + 25 + 3], 
                    fill=(255, 255, 255, 255))
    
    # Coins
    coin_positions = [(380, 200, 16), (400, 220, 14)]
    for x, y, r in coin_positions:
        # Coin gradient effect
        for i in range(r):
            alpha = int(255 * (1 - i / r * 0.3))
            color = (255, 215, 0, alpha)
            draw.ellipse([x - r + i, y - r + i, x + r - i, y + r - i], fill=color)
        
        draw.ellipse([x-r, y-r, x+r, y+r], fill=(255, 215, 0, 255), 
                    outline=(255, 255, 255, 255), width=2)
        draw.text((x, y), "IQD", fill=(139, 69, 19, 255), font=font_small, anchor="mm")
    
    # Title text
    draw.text((256, 450), "محاسب ديون", fill=(255, 255, 255, 255), 
             font=font_title, anchor="mm")
    draw.text((256, 480), "احترافي", fill=(255, 255, 255, 230), 
             font=font_medium, anchor="mm")
    
    # Save the icon
    output_path = "assets/icons/app_icon.png"
    img.save(output_path, "PNG")
    print(f"App icon created successfully: {output_path}")
    
    # Create smaller versions for different platforms
    sizes = [192, 144, 96, 72, 48, 36]
    for s in sizes:
        small_img = img.resize((s, s), Image.Resampling.LANCZOS)
        small_img.save(f"assets/icons/app_icon_{s}.png", "PNG")
        print(f"Created {s}x{s} icon")

if __name__ == "__main__":
    create_app_icon()
