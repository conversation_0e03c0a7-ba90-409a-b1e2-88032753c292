# محاسب الديون - Debt Accountant

تطبيق محاسب ديون احترافي مطور بـ Flutter لإدارة ديون العملاء والتسديدات.

## المميزات الرئيسية

### 🔍 محرك بحث متطور
- بحث سريع في قائمة العملاء
- البحث بالاسم أو رقم الهاتف
- واجهة بحث احترافية مع تأثيرات بصرية

### 👥 إدارة العملاء
- إضافة عملاء جدد مع معلومات كاملة
- تعديل بيانات العملاء
- عرض قائمة العملاء مع بطاقات أنيقة
- معلومات العميل: الاسم، الهاتف، البريد الإلكتروني، العنوان

### 💰 إدارة الديون
- إضافة ديون جديدة مع تفاصيل شاملة:
  - اسم الصنف
  - الكمية
  - المبلغ
  - نوع الكارت (نقدي، فيزا، ماستركارد، أمريكان إكسبريس، أخرى)
  - ملاحظات
  - تاريخ القيد
  - تاريخ الاستحقاق
- تعديل وحذف الديون
- عرض حالة الدين (معلق، مدفوع جزئياً، مدفوع)
- تمييز الديون المتأخرة

### 💳 نظام التسديدات
- تسديد كامل أو جزئي
- تتبع جميع عمليات التسديد
- إمكانية إرجاع التسديدات
- انتقال تلقائي للديون المدفوعة إلى قائمة التسديدات

### 📊 الإحصائيات والتقارير
- إجمالي العملاء
- إجمالي الديون
- إجمالي المبالغ المدفوعة
- المبالغ المتبقية
- عدد الديون المتأخرة
- نسبة التحصيل مع مؤشر بصري

### 🎨 واجهة مستخدم احترافية
- تصميم Material Design 3
- ألوان متناسقة ومريحة للعين
- أزرار وصول سريع
- بطاقات منظمة وجذابة
- تأثيرات بصرية ناعمة

## البنية التقنية

### التقنيات المستخدمة
- **Flutter**: إطار العمل الرئيسي
- **Provider**: إدارة الحالة
- **SQLite**: قاعدة البيانات المحلية
- **Material Design 3**: نظام التصميم

### بنية المشروع
```
lib/
├── models/           # نماذج البيانات
│   ├── customer.dart
│   ├── debt.dart
│   └── payment.dart
├── database/         # قاعدة البيانات
│   └── database_helper.dart
├── providers/        # مزودي الحالة
│   ├── customer_provider.dart
│   └── debt_provider.dart
├── screens/          # الشاشات
│   ├── home_screen.dart
│   ├── customer_detail_screen.dart
│   ├── add_customer_screen.dart
│   ├── add_debt_screen.dart
│   └── statistics_screen.dart
├── widgets/          # المكونات المخصصة
│   ├── customer_card.dart
│   ├── debt_card.dart
│   ├── debts_tab.dart
│   ├── payments_tab.dart
│   ├── search_bar_widget.dart
│   └── quick_access_buttons.dart
└── main.dart         # نقطة البداية
```

## التثبيت والتشغيل

### المتطلبات
- Flutter SDK (3.8.0 أو أحدث)
- Dart SDK
- Android Studio أو VS Code
- جهاز Android أو محاكي

### خطوات التثبيت
1. استنساخ المشروع:
```bash
git clone [repository-url]
cd mahasb
```

2. تحميل التبعيات:
```bash
flutter pub get
```

3. تشغيل التطبيق:
```bash
flutter run
```

## الاستخدام

### إضافة عميل جديد
1. اضغط على زر "إضافة عميل" في الشاشة الرئيسية
2. املأ بيانات العميل (الاسم والهاتف مطلوبان)
3. اضغط "حفظ"

### إضافة دين
1. ادخل إلى صفحة العميل
2. اضغط على زر "إضافة دين"
3. املأ تفاصيل الدين
4. حدد تاريخ القيد والاستحقاق
5. اضغط "حفظ"

### التسديد
1. في قائمة الديون، اضغط على "تسديد كامل" أو "تسديد جزئي"
2. أدخل المبلغ (في حالة التسديد الجزئي)
3. أضف ملاحظات إذا لزم الأمر
4. اضغط "تسديد"

### عرض الإحصائيات
اضغط على زر "الإحصائيات" في أسفل الشاشة الرئيسية لعرض تقرير شامل

## المميزات المستقبلية
- [ ] تصدير التقارير إلى PDF
- [ ] نسخ احتياطي واستعادة البيانات
- [ ] إشعارات للديون المتأخرة
- [ ] رسوم بيانية للإحصائيات
- [ ] دعم عملات متعددة
- [ ] طباعة الفواتير

## المساهمة
نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للاقتراحات والتحسينات.

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.
