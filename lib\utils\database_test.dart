import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../models/customer.dart';
import '../models/card_inventory.dart';
import '../models/custom_card_type.dart';

class DatabaseTest {
  static final DatabaseHelper _dbHelper = DatabaseHelper();

  /// اختبار شامل لقاعدة البيانات
  static Future<Map<String, dynamic>> runDatabaseTests() async {
    final results = <String, dynamic>{};
    
    try {
      debugPrint('🔍 بدء اختبار قاعدة البيانات...');
      
      // اختبار الاتصال بقاعدة البيانات
      results['connection'] = await _testDatabaseConnection();
      
      // اختبار الجداول
      results['tables'] = await _testTables();
      
      // اختبار العمليات الأساسية
      results['operations'] = await _testBasicOperations();
      
      // اختبار الإحصائيات
      results['statistics'] = await _testStatistics();
      
      // اختبار التنظيف
      results['cleanup'] = await _testCleanup();
      
      debugPrint('✅ انتهى اختبار قاعدة البيانات بنجاح');
      
    } catch (e) {
      debugPrint('❌ خطأ في اختبار قاعدة البيانات: $e');
      results['error'] = e.toString();
    }
    
    return results;
  }

  /// اختبار الاتصال بقاعدة البيانات
  static Future<Map<String, dynamic>> _testDatabaseConnection() async {
    try {
      final db = await _dbHelper.database;
      final version = await db.rawQuery('PRAGMA user_version');
      final versionNumber = version.isNotEmpty ? version.first['user_version'] : 0;

      return {
        'success': true,
        'version': versionNumber,
        'message': 'تم الاتصال بقاعدة البيانات بنجاح'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في الاتصال بقاعدة البيانات'
      };
    }
  }

  /// اختبار وجود الجداول
  static Future<Map<String, dynamic>> _testTables() async {
    try {
      final db = await _dbHelper.database;
      
      final expectedTables = [
        'customers',
        'debts', 
        'payments',
        'custom_card_types',
        'card_stocks',
        'card_profits',
        'card_inventories'
      ];
      
      final existingTables = <String>[];
      final missingTables = <String>[];
      
      for (final tableName in expectedTables) {
        final result = await db.rawQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [tableName]
        );
        
        if (result.isNotEmpty) {
          existingTables.add(tableName);
        } else {
          missingTables.add(tableName);
        }
      }
      
      return {
        'success': missingTables.isEmpty,
        'existing': existingTables,
        'missing': missingTables,
        'message': missingTables.isEmpty 
          ? 'جميع الجداول موجودة' 
          : 'بعض الجداول مفقودة: ${missingTables.join(', ')}'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في فحص الجداول'
      };
    }
  }

  /// اختبار العمليات الأساسية
  static Future<Map<String, dynamic>> _testBasicOperations() async {
    try {
      final results = <String, dynamic>{};
      
      // اختبار العملاء
      results['customers'] = await _testCustomerOperations();
      
      // اختبار المخزون
      results['inventory'] = await _testInventoryOperations();
      
      // اختبار أنواع البطاقات المخصصة
      results['customCardTypes'] = await _testCustomCardTypeOperations();
      
      return {
        'success': true,
        'results': results,
        'message': 'تم اختبار العمليات الأساسية بنجاح'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار العمليات الأساسية'
      };
    }
  }

  /// اختبار عمليات العملاء
  static Future<Map<String, dynamic>> _testCustomerOperations() async {
    try {
      // إضافة عميل تجريبي
      final testCustomer = Customer(
        name: 'عميل تجريبي',
        phone: '1234567890',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final customerId = await _dbHelper.insertCustomer(testCustomer);
      
      // قراءة العملاء
      final customers = await _dbHelper.getAllCustomers();
      
      // حذف العميل التجريبي
      await _dbHelper.deleteCustomer(customerId);
      
      return {
        'success': true,
        'customerId': customerId,
        'customersCount': customers.length,
        'message': 'عمليات العملاء تعمل بشكل صحيح'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار عمليات العملاء'
      };
    }
  }

  /// اختبار عمليات المخزون
  static Future<Map<String, dynamic>> _testInventoryOperations() async {
    try {
      // إضافة عنصر مخزون تجريبي
      final testInventory = CardInventory(
        cardType: 'اختبار',
        quantity: 100,
      );
      
      final inventoryId = await _dbHelper.insertCardInventory(testInventory);
      
      // قراءة المخزون
      final inventories = await _dbHelper.getAllCardInventories();
      
      // حذف العنصر التجريبي
      await _dbHelper.deleteCardInventory(inventoryId);
      
      return {
        'success': true,
        'inventoryId': inventoryId,
        'inventoriesCount': inventories.length,
        'message': 'عمليات المخزون تعمل بشكل صحيح'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار عمليات المخزون'
      };
    }
  }

  /// اختبار عمليات أنواع البطاقات المخصصة
  static Future<Map<String, dynamic>> _testCustomCardTypeOperations() async {
    try {
      // إضافة نوع بطاقة تجريبي
      final testCardType = CustomCardType(
        name: 'test_card',
        displayName: 'بطاقة تجريبية',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      final cardTypeId = await _dbHelper.insertCustomCardType(testCardType);
      
      // قراءة أنواع البطاقات
      final cardTypes = await _dbHelper.getAllCustomCardTypes();
      
      // حذف النوع التجريبي
      await _dbHelper.deleteCustomCardType(cardTypeId);
      
      return {
        'success': true,
        'cardTypeId': cardTypeId,
        'cardTypesCount': cardTypes.length,
        'message': 'عمليات أنواع البطاقات تعمل بشكل صحيح'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار عمليات أنواع البطاقات'
      };
    }
  }

  /// اختبار الإحصائيات
  static Future<Map<String, dynamic>> _testStatistics() async {
    try {
      final stats = await _dbHelper.getDatabaseInfo();
      
      return {
        'success': true,
        'stats': stats,
        'message': 'الإحصائيات تعمل بشكل صحيح'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار الإحصائيات'
      };
    }
  }

  /// اختبار التنظيف
  static Future<Map<String, dynamic>> _testCleanup() async {
    try {
      await _dbHelper.cleanupDatabase();
      
      return {
        'success': true,
        'message': 'تنظيف قاعدة البيانات يعمل بشكل صحيح'
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'message': 'فشل في اختبار التنظيف'
      };
    }
  }

  /// طباعة تقرير مفصل
  static void printTestReport(Map<String, dynamic> results) {
    debugPrint('\n📊 تقرير اختبار قاعدة البيانات:');
    debugPrint('=' * 50);
    
    results.forEach((key, value) {
      if (value is Map<String, dynamic>) {
        debugPrint('\n🔸 $key:');
        if (value['success'] == true) {
          debugPrint('  ✅ ${value['message']}');
        } else {
          debugPrint('  ❌ ${value['message']}');
          if (value['error'] != null) {
            debugPrint('  🔍 خطأ: ${value['error']}');
          }
        }
      }
    });
    
    debugPrint('\n${'=' * 50}');
  }
}
