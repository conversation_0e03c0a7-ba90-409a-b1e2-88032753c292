@echo off
echo ========================================
echo    Auto Commit Script
echo ========================================
echo.

REM Add all changes
git add .

REM Check if there are changes to commit
git diff --cached --quiet
if %ERRORLEVEL% neq 0 (
    echo Changes detected - creating auto commit...
    git commit -m "Auto commit: %DATE% %TIME%"
    echo Changes committed successfully!
) else (
    echo No changes to commit.
)

echo.
echo Script completed.
pause
